@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #1a1a1a;
  --primary: #006233;
  --primary-light: #16a34a;
  --primary-dark: #14532d;
  --secondary: #f7c608;
  --secondary-light: #fbbf24;
  --secondary-dark: #d97706;
  --accent: #059669;
  --accent-light: #10b981;
  --accent-dark: #047857;
  --neutral: #374151;
  --neutral-light: #6b7280;
  --neutral-dark: #1f2937;
  --gray-dark: #f8fafc;
  --gray-medium: #f1f5f9;
  --gray-light: #ffffff;
  --text-muted: #64748b;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-primary: var(--primary);
  --color-primary-light: var(--primary-light);
  --color-primary-dark: var(--primary-dark);
  --color-secondary: var(--secondary);
  --color-secondary-light: var(--secondary-light);
  --color-secondary-dark: var(--secondary-dark);
  --color-accent: var(--accent);
  --color-accent-light: var(--accent-light);
  --color-accent-dark: var(--accent-dark);
  --color-neutral: var(--neutral);
  --color-neutral-light: var(--neutral-light);
  --color-neutral-dark: var(--neutral-dark);
  --color-gray-dark: var(--gray-dark);
  --color-gray-medium: var(--gray-medium);
  --color-gray-light: var(--gray-light);
  --color-text-muted: var(--text-muted);
  --font-sans: var(--font-inter);
  --font-serif: var(--font-playfair);
}

* {
  box-sizing: border-box;
}

body {
  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);
  color: var(--foreground);
  font-family: var(--font-inter), sans-serif;
  line-height: 1.6;
  overflow-x: hidden;
}

/* Premium Glass Effect */
.glass-effect {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
}

.glass-effect-light {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(201, 169, 110, 0.2);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.1);
}

/* Green Theme Gradients */
.hero-gradient {
  background: linear-gradient(135deg, #ffffff 0%, #f0fdf4 50%, #dcfce7 100%);
  position: relative;
}

.hero-gradient::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 30% 20%, rgba(6, 98, 51, 0.05) 0%, transparent 50%);
  pointer-events: none;
}

.green-gradient {
  background: linear-gradient(135deg, #006233 0%, #16a34a 50%, #059669 100%);
}

.gold-gradient {
  background: linear-gradient(135deg, #f7c608 0%, #fbbf24 50%, #d97706 100%);
}

.business-card {
  background: white;
  border: 1px solid rgba(6, 98, 51, 0.1);
  box-shadow:
    0 4px 20px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 12px;
}

.business-card:hover {
  transform: translateY(-4px);
  box-shadow:
    0 12px 40px rgba(0, 0, 0, 0.12),
    0 0 20px rgba(6, 98, 51, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.9);
  border-color: rgba(6, 98, 51, 0.2);
}

.stats-card {
  background: linear-gradient(145deg, #f8fafc 0%, #ffffff 100%);
  border: 1px solid rgba(6, 98, 51, 0.1);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
}

.stats-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}

/* Green Theme Typography */
.heading-luxury {
  background: linear-gradient(135deg, #006233 0%, #16a34a 50%, #059669 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 2px 4px rgba(0, 98, 51, 0.2);
}

.text-green-gradient {
  background: linear-gradient(135deg, #006233 0%, #16a34a 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.text-gold-gradient {
  background: linear-gradient(135deg, #f7c608 0%, #fbbf24 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.text-primary {
  color: #006233;
}

.text-secondary {
  color: #f7c608;
}

/* Green Theme Buttons */
.btn-green {
  background: linear-gradient(135deg, #006233 0%, #16a34a 100%);
  border: 1px solid rgba(6, 98, 51, 0.3);
  color: white;
  box-shadow:
    0 4px 12px rgba(6, 98, 51, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.btn-green:hover {
  background: linear-gradient(135deg, #14532d 0%, #006233 100%);
  transform: translateY(-2px);
  box-shadow:
    0 8px 20px rgba(6, 98, 51, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.btn-gold {
  background: linear-gradient(135deg, #f7c608 0%, #fbbf24 100%);
  border: 1px solid rgba(247, 198, 8, 0.3);
  color: #1a1a1a;
  box-shadow:
    0 4px 12px rgba(247, 198, 8, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.btn-gold:hover {
  background: linear-gradient(135deg, #d97706 0%, #f7c608 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(247, 198, 8, 0.4);
}

.btn-outline-green {
  background: transparent;
  border: 2px solid #006233;
  color: #006233;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.btn-outline-green::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 0;
  height: 100%;
  background: linear-gradient(135deg, #006233 0%, #16a34a 100%);
  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: -1;
}

.btn-outline-green:hover::before {
  width: 100%;
}

.btn-outline-green:hover {
  color: white;
  border-color: #006233;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(6, 98, 51, 0.3);
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

.animate-fade-in-up {
  animation: fadeInUp 0.8s ease-out;
}

.animate-slide-in-right {
  animation: slideInRight 0.8s ease-out;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

/* Clean Navigation */
.nav-clean {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(6, 98, 51, 0.1);
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

/* Section Styling */
.section-padding {
  padding: 8rem 0;
}

.section-green {
  background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
  position: relative;
}

.section-green::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 80%, rgba(6, 98, 51, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(247, 198, 8, 0.03) 0%, transparent 50%);
  pointer-events: none;
}

.section-white {
  background: #ffffff;
}

.section-light {
  background: #f8fafc;
}

/* Green Theme Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #006233 0%, #16a34a 100%);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #16a34a 0%, #059669 100%);
}
