@import "tailwindcss";

:root {
  --background: #0a0a0a;
  --foreground: #ffffff;
  --primary: #1e3a8a;
  --primary-light: #3b82f6;
  --primary-dark: #1e40af;
  --secondary: #dc2626;
  --secondary-light: #ef4444;
  --secondary-dark: #b91c1c;
  --accent: #f59e0b;
  --accent-light: #fbbf24;
  --accent-dark: #d97706;
  --neutral: #374151;
  --neutral-light: #6b7280;
  --neutral-dark: #1f2937;
  --gray-dark: #111827;
  --gray-medium: #1f2937;
  --gray-light: #f9fafb;
  --text-muted: #9ca3af;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-primary: var(--primary);
  --color-primary-light: var(--primary-light);
  --color-primary-dark: var(--primary-dark);
  --color-secondary: var(--secondary);
  --color-secondary-light: var(--secondary-light);
  --color-secondary-dark: var(--secondary-dark);
  --color-accent: var(--accent);
  --color-accent-light: var(--accent-light);
  --color-accent-dark: var(--accent-dark);
  --color-neutral: var(--neutral);
  --color-neutral-light: var(--neutral-light);
  --color-neutral-dark: var(--neutral-dark);
  --color-gray-dark: var(--gray-dark);
  --color-gray-medium: var(--gray-medium);
  --color-gray-light: var(--gray-light);
  --color-text-muted: var(--text-muted);
  --font-sans: var(--font-inter);
  --font-serif: var(--font-playfair);
}

* {
  box-sizing: border-box;
}

body {
  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);
  color: var(--foreground);
  font-family: var(--font-inter), sans-serif;
  line-height: 1.6;
  overflow-x: hidden;
}

/* Premium Glass Effect */
.glass-effect {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
}

.glass-effect-light {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(201, 169, 110, 0.2);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.1);
}

/* Premium Gradients */
.hero-gradient {
  background: linear-gradient(135deg, #111827 0%, #1e3a8a 50%, #1e40af 100%);
  position: relative;
}

.hero-gradient::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 30% 20%, rgba(245, 158, 11, 0.1) 0%, transparent 50%);
  pointer-events: none;
}

.accent-gradient {
  background: linear-gradient(135deg, #f59e0b 0%, #fbbf24 50%, #d97706 100%);
}

.secondary-gradient {
  background: linear-gradient(135deg, #dc2626 0%, #ef4444 50%, #b91c1c 100%);
}

.premium-card {
  background: linear-gradient(145deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.02) 100%);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(245, 158, 11, 0.2);
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.premium-card:hover {
  transform: translateY(-8px);
  box-shadow:
    0 30px 60px rgba(0, 0, 0, 0.5),
    0 0 40px rgba(245, 158, 11, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  border-color: rgba(245, 158, 11, 0.4);
}

/* Typography */
.heading-luxury {
  background: linear-gradient(135deg, #ffffff 0%, #fbbf24 50%, #f59e0b 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.text-accent-gradient {
  background: linear-gradient(135deg, #f59e0b 0%, #fbbf24 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.text-primary-gradient {
  background: linear-gradient(135deg, #3b82f6 0%, #1e3a8a 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.text-secondary-gradient {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Premium Buttons */
.btn-premium {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  border: 1px solid rgba(245, 158, 11, 0.3);
  box-shadow:
    0 8px 20px rgba(245, 158, 11, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.btn-premium::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn-premium:hover::before {
  left: 100%;
}

.btn-premium:hover {
  transform: translateY(-2px);
  box-shadow:
    0 12px 30px rgba(245, 158, 11, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.btn-outline-premium {
  background: transparent;
  border: 2px solid rgba(245, 158, 11, 0.6);
  color: #f59e0b;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.btn-outline-premium::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 0;
  height: 100%;
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: -1;
}

.btn-outline-premium:hover::before {
  width: 100%;
}

.btn-outline-premium:hover {
  color: #111827;
  border-color: #f59e0b;
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(245, 158, 11, 0.3);
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

.animate-fade-in-up {
  animation: fadeInUp 0.8s ease-out;
}

.animate-slide-in-right {
  animation: slideInRight 0.8s ease-out;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

/* Premium Navigation */
.nav-luxury {
  background: rgba(17, 24, 39, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(245, 158, 11, 0.2);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

/* Section Styling */
.section-padding {
  padding: 8rem 0;
}

.section-dark {
  background: linear-gradient(135deg, #111827 0%, #1f2937 100%);
  position: relative;
}

.section-dark::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 80%, rgba(245, 158, 11, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(30, 58, 138, 0.05) 0%, transparent 50%);
  pointer-events: none;
}

/* Scrollbar Styling */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #1f2937;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
}
