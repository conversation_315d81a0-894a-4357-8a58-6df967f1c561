{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Dev2/hassona2/nhm-group-website/src/app/companies/page.tsx"], "sourcesContent": ["import Image from \"next/image\";\nimport Link from \"next/link\";\n\nexport default function Companies() {\n  return (\n    <div className=\"min-h-screen\">\n      {/* Navigation */}\n      <nav className=\"bg-white shadow-sm border-b border-gray-200 sticky top-0 z-50\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center h-16\">\n            <div className=\"flex items-center\">\n              <Link href=\"/\">\n                <h1 className=\"text-2xl font-bold text-blue-900 font-serif\">NHM Group Holdings</h1>\n              </Link>\n            </div>\n            <div className=\"hidden md:block\">\n              <div className=\"ml-10 flex items-baseline space-x-8\">\n                <Link href=\"/\" className=\"text-gray-600 hover:text-yellow-600 transition-colors px-3 py-2 text-sm font-medium\">\n                  Home\n                </Link>\n                <Link href=\"/about\" className=\"text-gray-600 hover:text-yellow-600 transition-colors px-3 py-2 text-sm font-medium\">\n                  About Us\n                </Link>\n                <Link href=\"/companies\" className=\"text-blue-900 hover:text-yellow-600 transition-colors px-3 py-2 text-sm font-medium\">\n                  Our Companies\n                </Link>\n                <Link href=\"/clients\" className=\"text-gray-600 hover:text-yellow-600 transition-colors px-3 py-2 text-sm font-medium\">\n                  Clients & Partners\n                </Link>\n                <Link href=\"/contact\" className=\"text-gray-600 hover:text-yellow-600 transition-colors px-3 py-2 text-sm font-medium\">\n                  Contact\n                </Link>\n              </div>\n            </div>\n          </div>\n        </div>\n      </nav>\n\n      {/* Hero Section */}\n      <section className=\"bg-gradient-to-r from-blue-900 to-blue-700 text-white py-20\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n          <h1 className=\"text-5xl font-bold font-serif mb-6\">Our Portfolio</h1>\n          <p className=\"text-xl text-blue-100 max-w-3xl mx-auto\">\n            Discover our diverse range of companies spanning multiple industries and markets\n          </p>\n        </div>\n      </section>\n\n      {/* Portfolio Overview */}\n      <section className=\"py-20\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-4xl font-bold font-serif text-blue-900 mb-4\">Diversified Excellence</h2>\n            <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n              Our strategic investments across key sectors drive sustainable growth and create lasting value\n            </p>\n          </div>\n          \n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n            {/* Automotive Division */}\n            <Link href=\"/companies/automotive\" className=\"group\">\n              <div className=\"bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 group-hover:scale-105\">\n                <div className=\"h-48 bg-gradient-to-br from-blue-900 to-blue-600 relative overflow-hidden\">\n                  <div className=\"absolute inset-0 bg-black bg-opacity-20\"></div>\n                  <div className=\"absolute bottom-4 left-4 text-white\">\n                    <h3 className=\"text-2xl font-bold\">Automotive</h3>\n                    <p className=\"text-blue-100\">Leading dealerships & services</p>\n                  </div>\n                </div>\n                <div className=\"p-6\">\n                  <h3 className=\"text-xl font-bold text-blue-900 mb-2\">Automotive Division</h3>\n                  <p className=\"text-gray-600 mb-4\">\n                    Leading automotive dealerships and services, including our flagship ISUZU operations in Libya.\n                  </p>\n                  <div className=\"flex items-center text-yellow-600 font-semibold group-hover:text-yellow-700\">\n                    Learn More \n                    <span className=\"ml-2 transform group-hover:translate-x-1 transition-transform\">→</span>\n                  </div>\n                </div>\n              </div>\n            </Link>\n\n            {/* Technology Solutions */}\n            <Link href=\"/companies/technology\" className=\"group\">\n              <div className=\"bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 group-hover:scale-105\">\n                <div className=\"h-48 bg-gradient-to-br from-yellow-400 to-yellow-500 relative overflow-hidden\">\n                  <div className=\"absolute inset-0 bg-black bg-opacity-20\"></div>\n                  <div className=\"absolute bottom-4 left-4 text-white\">\n                    <h3 className=\"text-2xl font-bold\">Technology</h3>\n                    <p className=\"text-yellow-100\">Innovation & digital solutions</p>\n                  </div>\n                </div>\n                <div className=\"p-6\">\n                  <h3 className=\"text-xl font-bold text-blue-900 mb-2\">Technology Solutions</h3>\n                  <p className=\"text-gray-600 mb-4\">\n                    Innovative technology companies driving digital transformation across various sectors.\n                  </p>\n                  <div className=\"flex items-center text-yellow-600 font-semibold group-hover:text-yellow-700\">\n                    Learn More \n                    <span className=\"ml-2 transform group-hover:translate-x-1 transition-transform\">→</span>\n                  </div>\n                </div>\n              </div>\n            </Link>\n\n            {/* Real Estate */}\n            <Link href=\"/companies/real-estate\" className=\"group\">\n              <div className=\"bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 group-hover:scale-105\">\n                <div className=\"h-48 bg-gradient-to-br from-gray-600 to-gray-800 relative overflow-hidden\">\n                  <div className=\"absolute inset-0 bg-black bg-opacity-20\"></div>\n                  <div className=\"absolute bottom-4 left-4 text-white\">\n                    <h3 className=\"text-2xl font-bold\">Real Estate</h3>\n                    <p className=\"text-gray-100\">Strategic investments & development</p>\n                  </div>\n                </div>\n                <div className=\"p-6\">\n                  <h3 className=\"text-xl font-bold text-blue-900 mb-2\">Real Estate</h3>\n                  <p className=\"text-gray-600 mb-4\">\n                    Strategic real estate investments and development projects in key markets.\n                  </p>\n                  <div className=\"flex items-center text-yellow-600 font-semibold group-hover:text-yellow-700\">\n                    Learn More \n                    <span className=\"ml-2 transform group-hover:translate-x-1 transition-transform\">→</span>\n                  </div>\n                </div>\n              </div>\n            </Link>\n\n            {/* Manufacturing */}\n            <Link href=\"/companies/manufacturing\" className=\"group\">\n              <div className=\"bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 group-hover:scale-105\">\n                <div className=\"h-48 bg-gradient-to-br from-green-600 to-green-700 relative overflow-hidden\">\n                  <div className=\"absolute inset-0 bg-black bg-opacity-20\"></div>\n                  <div className=\"absolute bottom-4 left-4 text-white\">\n                    <h3 className=\"text-2xl font-bold\">Manufacturing</h3>\n                    <p className=\"text-green-100\">Industrial excellence</p>\n                  </div>\n                </div>\n                <div className=\"p-6\">\n                  <h3 className=\"text-xl font-bold text-blue-900 mb-2\">Manufacturing</h3>\n                  <p className=\"text-gray-600 mb-4\">\n                    Advanced manufacturing facilities producing high-quality products for global markets.\n                  </p>\n                  <div className=\"flex items-center text-yellow-600 font-semibold group-hover:text-yellow-700\">\n                    Learn More \n                    <span className=\"ml-2 transform group-hover:translate-x-1 transition-transform\">→</span>\n                  </div>\n                </div>\n              </div>\n            </Link>\n\n            {/* Trading & Distribution */}\n            <Link href=\"/companies/trading\" className=\"group\">\n              <div className=\"bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 group-hover:scale-105\">\n                <div className=\"h-48 bg-gradient-to-br from-purple-600 to-purple-700 relative overflow-hidden\">\n                  <div className=\"absolute inset-0 bg-black bg-opacity-20\"></div>\n                  <div className=\"absolute bottom-4 left-4 text-white\">\n                    <h3 className=\"text-2xl font-bold\">Trading</h3>\n                    <p className=\"text-purple-100\">Global distribution network</p>\n                  </div>\n                </div>\n                <div className=\"p-6\">\n                  <h3 className=\"text-xl font-bold text-blue-900 mb-2\">Trading & Distribution</h3>\n                  <p className=\"text-gray-600 mb-4\">\n                    Comprehensive trading and distribution services connecting markets across regions.\n                  </p>\n                  <div className=\"flex items-center text-yellow-600 font-semibold group-hover:text-yellow-700\">\n                    Learn More \n                    <span className=\"ml-2 transform group-hover:translate-x-1 transition-transform\">→</span>\n                  </div>\n                </div>\n              </div>\n            </Link>\n\n            {/* Financial Services */}\n            <Link href=\"/companies/financial\" className=\"group\">\n              <div className=\"bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 group-hover:scale-105\">\n                <div className=\"h-48 bg-gradient-to-br from-indigo-600 to-indigo-700 relative overflow-hidden\">\n                  <div className=\"absolute inset-0 bg-black bg-opacity-20\"></div>\n                  <div className=\"absolute bottom-4 left-4 text-white\">\n                    <h3 className=\"text-2xl font-bold\">Financial</h3>\n                    <p className=\"text-indigo-100\">Investment & advisory services</p>\n                  </div>\n                </div>\n                <div className=\"p-6\">\n                  <h3 className=\"text-xl font-bold text-blue-900 mb-2\">Financial Services</h3>\n                  <p className=\"text-gray-600 mb-4\">\n                    Comprehensive financial services including investment management and advisory solutions.\n                  </p>\n                  <div className=\"flex items-center text-yellow-600 font-semibold group-hover:text-yellow-700\">\n                    Learn More \n                    <span className=\"ml-2 transform group-hover:translate-x-1 transition-transform\">→</span>\n                  </div>\n                </div>\n              </div>\n            </Link>\n          </div>\n        </div>\n      </section>\n\n      {/* Performance Metrics */}\n      <section className=\"py-20 bg-gray-50\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-4xl font-bold font-serif text-blue-900 mb-4\">Portfolio Performance</h2>\n            <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n              Our companies deliver consistent growth and value creation across all sectors\n            </p>\n          </div>\n          \n          <div className=\"grid grid-cols-1 md:grid-cols-4 gap-8\">\n            <div className=\"text-center\">\n              <div className=\"text-4xl font-bold text-blue-900 mb-2\">15+</div>\n              <div className=\"text-gray-600\">Portfolio Companies</div>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"text-4xl font-bold text-yellow-600 mb-2\">8</div>\n              <div className=\"text-gray-600\">Industry Sectors</div>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"text-4xl font-bold text-green-600 mb-2\">25+</div>\n              <div className=\"text-gray-600\">Years Experience</div>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"text-4xl font-bold text-purple-600 mb-2\">5</div>\n              <div className=\"text-gray-600\">Countries</div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Call to Action */}\n      <section className=\"bg-gradient-to-r from-blue-900 to-blue-700 text-white py-20\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n          <h2 className=\"text-4xl font-bold font-serif mb-6\">Explore Investment Opportunities</h2>\n          <p className=\"text-xl mb-8 text-blue-100 max-w-3xl mx-auto\">\n            Discover how our diverse portfolio can create value for your investment goals\n          </p>\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n            <Link \n              href=\"/contact\" \n              className=\"bg-yellow-400 text-blue-900 px-8 py-3 rounded-lg font-semibold hover:bg-yellow-300 transition-colors\"\n            >\n              Contact Our Team\n            </Link>\n            <Link \n              href=\"/clients\" \n              className=\"border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-blue-900 transition-colors\"\n            >\n              View Our Partners\n            </Link>\n          </div>\n        </div>\n      </section>\n\n      {/* Footer */}\n      <footer className=\"bg-blue-900 text-white py-12\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"grid grid-cols-1 md:grid-cols-4 gap-8\">\n            <div className=\"col-span-1 md:col-span-2\">\n              <h3 className=\"text-2xl font-bold font-serif mb-4\">NHM Group Holdings</h3>\n              <p className=\"text-blue-200 mb-4\">\n                Building tomorrow's success through strategic investments and operational excellence.\n              </p>\n            </div>\n            <div>\n              <h4 className=\"text-lg font-semibold mb-4\">Quick Links</h4>\n              <ul className=\"space-y-2\">\n                <li><Link href=\"/about\" className=\"text-blue-200 hover:text-yellow-400 transition-colors\">About Us</Link></li>\n                <li><Link href=\"/companies\" className=\"text-blue-200 hover:text-yellow-400 transition-colors\">Our Companies</Link></li>\n                <li><Link href=\"/clients\" className=\"text-blue-200 hover:text-yellow-400 transition-colors\">Clients & Partners</Link></li>\n                <li><Link href=\"/contact\" className=\"text-blue-200 hover:text-yellow-400 transition-colors\">Contact</Link></li>\n              </ul>\n            </div>\n            <div>\n              <h4 className=\"text-lg font-semibold mb-4\">Contact Info</h4>\n              <p className=\"text-blue-200 mb-2\">Email: <EMAIL></p>\n              <p className=\"text-blue-200 mb-2\">Phone: +****************</p>\n            </div>\n          </div>\n          <div className=\"border-t border-blue-800 mt-8 pt-8 text-center\">\n            <p className=\"text-blue-200\">&copy; 2024 NHM Group Holdings. All rights reserved.</p>\n          </div>\n        </div>\n      </footer>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;8CACT,cAAA,8OAAC;wCAAG,WAAU;kDAA8C;;;;;;;;;;;;;;;;0CAGhE,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAI,WAAU;sDAAsF;;;;;;sDAG/G,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAS,WAAU;sDAAsF;;;;;;sDAGpH,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAa,WAAU;sDAAsF;;;;;;sDAGxH,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAW,WAAU;sDAAsF;;;;;;sDAGtH,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAW,WAAU;sDAAsF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUhI,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAqC;;;;;;sCACnD,8OAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;;;;;;0BAO3D,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAmD;;;;;;8CACjE,8OAAC;oCAAE,WAAU;8CAA0C;;;;;;;;;;;;sCAKzD,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAwB,WAAU;8CAC3C,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAG,WAAU;0EAAqB;;;;;;0EACnC,8OAAC;gEAAE,WAAU;0EAAgB;;;;;;;;;;;;;;;;;;0DAGjC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAuC;;;;;;kEACrD,8OAAC;wDAAE,WAAU;kEAAqB;;;;;;kEAGlC,8OAAC;wDAAI,WAAU;;4DAA8E;0EAE3F,8OAAC;gEAAK,WAAU;0EAAgE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAOxF,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAwB,WAAU;8CAC3C,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAG,WAAU;0EAAqB;;;;;;0EACnC,8OAAC;gEAAE,WAAU;0EAAkB;;;;;;;;;;;;;;;;;;0DAGnC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAuC;;;;;;kEACrD,8OAAC;wDAAE,WAAU;kEAAqB;;;;;;kEAGlC,8OAAC;wDAAI,WAAU;;4DAA8E;0EAE3F,8OAAC;gEAAK,WAAU;0EAAgE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAOxF,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAyB,WAAU;8CAC5C,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAG,WAAU;0EAAqB;;;;;;0EACnC,8OAAC;gEAAE,WAAU;0EAAgB;;;;;;;;;;;;;;;;;;0DAGjC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAuC;;;;;;kEACrD,8OAAC;wDAAE,WAAU;kEAAqB;;;;;;kEAGlC,8OAAC;wDAAI,WAAU;;4DAA8E;0EAE3F,8OAAC;gEAAK,WAAU;0EAAgE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAOxF,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAA2B,WAAU;8CAC9C,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAG,WAAU;0EAAqB;;;;;;0EACnC,8OAAC;gEAAE,WAAU;0EAAiB;;;;;;;;;;;;;;;;;;0DAGlC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAuC;;;;;;kEACrD,8OAAC;wDAAE,WAAU;kEAAqB;;;;;;kEAGlC,8OAAC;wDAAI,WAAU;;4DAA8E;0EAE3F,8OAAC;gEAAK,WAAU;0EAAgE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAOxF,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAqB,WAAU;8CACxC,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAG,WAAU;0EAAqB;;;;;;0EACnC,8OAAC;gEAAE,WAAU;0EAAkB;;;;;;;;;;;;;;;;;;0DAGnC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAuC;;;;;;kEACrD,8OAAC;wDAAE,WAAU;kEAAqB;;;;;;kEAGlC,8OAAC;wDAAI,WAAU;;4DAA8E;0EAE3F,8OAAC;gEAAK,WAAU;0EAAgE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAOxF,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAuB,WAAU;8CAC1C,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAG,WAAU;0EAAqB;;;;;;0EACnC,8OAAC;gEAAE,WAAU;0EAAkB;;;;;;;;;;;;;;;;;;0DAGnC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAuC;;;;;;kEACrD,8OAAC;wDAAE,WAAU;kEAAqB;;;;;;kEAGlC,8OAAC;wDAAI,WAAU;;4DAA8E;0EAE3F,8OAAC;gEAAK,WAAU;0EAAgE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAU9F,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAmD;;;;;;8CACjE,8OAAC;oCAAE,WAAU;8CAA0C;;;;;;;;;;;;sCAKzD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAwC;;;;;;sDACvD,8OAAC;4CAAI,WAAU;sDAAgB;;;;;;;;;;;;8CAEjC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAA0C;;;;;;sDACzD,8OAAC;4CAAI,WAAU;sDAAgB;;;;;;;;;;;;8CAEjC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAyC;;;;;;sDACxD,8OAAC;4CAAI,WAAU;sDAAgB;;;;;;;;;;;;8CAEjC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAA0C;;;;;;sDACzD,8OAAC;4CAAI,WAAU;sDAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOvC,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAqC;;;;;;sCACnD,8OAAC;4BAAE,WAAU;sCAA+C;;;;;;sCAG5D,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;0BAQP,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAqC;;;;;;sDACnD,8OAAC;4CAAE,WAAU;sDAAqB;;;;;;;;;;;;8CAIpC,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAC3C,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;8DAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAS,WAAU;kEAAwD;;;;;;;;;;;8DAC1F,8OAAC;8DAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAa,WAAU;kEAAwD;;;;;;;;;;;8DAC9F,8OAAC;8DAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAW,WAAU;kEAAwD;;;;;;;;;;;8DAC5F,8OAAC;8DAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAW,WAAU;kEAAwD;;;;;;;;;;;;;;;;;;;;;;;8CAGhG,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAC3C,8OAAC;4CAAE,WAAU;sDAAqB;;;;;;sDAClC,8OAAC;4CAAE,WAAU;sDAAqB;;;;;;;;;;;;;;;;;;sCAGtC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMzC", "debugId": null}}, {"offset": {"line": 1261, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Dev2/hassona2/nhm-group-website/node_modules/next/dist/client/app-dir/link.js/proxy.cjs"], "sourcesContent": ["const { createClientModuleProxy } = require(\"react-server-dom-turbopack/server.edge\");\n\n__turbopack_context__.n(createClientModuleProxy(\"[project]/node_modules/next/dist/client/app-dir/link.js <module evaluation>\"));\n"], "names": [], "mappings": "AAAA,MAAM,EAAE,uBAAuB,EAAE;AAEjC,sBAAsB,CAAC,CAAC,wBAAwB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1268, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Dev2/hassona2/nhm-group-website/node_modules/next/dist/client/app-dir/link.js/proxy.cjs"], "sourcesContent": ["const { createClientModuleProxy } = require(\"react-server-dom-turbopack/server.edge\");\n\n__turbopack_context__.n(createClientModuleProxy(\"[project]/node_modules/next/dist/client/app-dir/link.js\"));\n"], "names": [], "mappings": "AAAA,MAAM,EAAE,uBAAuB,EAAE;AAEjC,sBAAsB,CAAC,CAAC,wBAAwB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1276, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Dev2/hassona2/nhm-group-website/node_modules/next/src/client/app-dir/link.tsx"], "sourcesContent": ["'use client'\n\nimport React, { createContext, useContext, useOptimistic, useRef } from 'react'\nimport type { UrlObject } from 'url'\nimport { formatUrl } from '../../shared/lib/router/utils/format-url'\nimport { AppRouterContext } from '../../shared/lib/app-router-context.shared-runtime'\nimport { PrefetchKind } from '../components/router-reducer/router-reducer-types'\nimport { useMergedRef } from '../use-merged-ref'\nimport { isAbsoluteUrl } from '../../shared/lib/utils'\nimport { addBasePath } from '../add-base-path'\nimport { warnOnce } from '../../shared/lib/utils/warn-once'\nimport type { PENDING_LINK_STATUS } from '../components/links'\nimport {\n  IDLE_LINK_STATUS,\n  mountLinkInstance,\n  onNavigationIntent,\n  unmountLinkForCurrentNavigation,\n  unmountPrefetchableInstance,\n  type LinkInstance,\n} from '../components/links'\nimport { isLocalURL } from '../../shared/lib/router/utils/is-local-url'\nimport { dispatchNavigateAction } from '../components/app-router-instance'\nimport { errorOnce } from '../../shared/lib/utils/error-once'\n\ntype Url = string | UrlObject\ntype RequiredKeys<T> = {\n  [K in keyof T]-?: {} extends Pick<T, K> ? never : K\n}[keyof T]\ntype OptionalKeys<T> = {\n  [K in keyof T]-?: {} extends Pick<T, K> ? K : never\n}[keyof T]\n\ntype OnNavigateEventHandler = (event: { preventDefault: () => void }) => void\n\ntype InternalLinkProps = {\n  /**\n   * **Required**. The path or URL to navigate to. It can also be an object (similar to `URL`).\n   *\n   * @example\n   * ```tsx\n   * // Navigate to /dashboard:\n   * <Link href=\"/dashboard\">Dashboard</Link>\n   *\n   * // Navigate to /about?name=test:\n   * <Link href={{ pathname: '/about', query: { name: 'test' } }}>\n   *   About\n   * </Link>\n   * ```\n   *\n   * @remarks\n   * - For external URLs, use a fully qualified URL such as `https://...`.\n   * - In the App Router, dynamic routes must not include bracketed segments in `href`.\n   */\n  href: Url\n\n  /**\n   * @deprecated v10.0.0: `href` props pointing to a dynamic route are\n   * automatically resolved and no longer require the `as` prop.\n   */\n  as?: Url\n\n  /**\n   * Replace the current `history` state instead of adding a new URL into the stack.\n   *\n   * @defaultValue `false`\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/about\" replace>\n   *   About (replaces the history state)\n   * </Link>\n   * ```\n   */\n  replace?: boolean\n\n  /**\n   * Whether to override the default scroll behavior. If `true`, Next.js attempts to maintain\n   * the scroll position if the newly navigated page is still visible. If not, it scrolls to the top.\n   *\n   * If `false`, Next.js will not modify the scroll behavior at all.\n   *\n   * @defaultValue `true`\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/dashboard\" scroll={false}>\n   *   No auto scroll\n   * </Link>\n   * ```\n   */\n  scroll?: boolean\n\n  /**\n   * Update the path of the current page without rerunning data fetching methods\n   * like `getStaticProps`, `getServerSideProps`, or `getInitialProps`.\n   *\n   * @remarks\n   * `shallow` only applies to the Pages Router. For the App Router, see the\n   * [following documentation](https://nextjs.org/docs/app/building-your-application/routing/linking-and-navigating#using-the-native-history-api).\n   *\n   * @defaultValue `false`\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/blog\" shallow>\n   *   Shallow navigation\n   * </Link>\n   * ```\n   */\n  shallow?: boolean\n\n  /**\n   * Forces `Link` to pass its `href` to the child component. Useful if the child is a custom\n   * component that wraps an `<a>` tag, or if you're using certain styling libraries.\n   *\n   * @defaultValue `false`\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/dashboard\" passHref>\n   *   <MyStyledAnchor>Dashboard</MyStyledAnchor>\n   * </Link>\n   * ```\n   */\n  passHref?: boolean\n\n  /**\n   * Prefetch the page in the background.\n   * Any `<Link />` that is in the viewport (initially or through scroll) will be prefetched.\n   * Prefetch can be disabled by passing `prefetch={false}`.\n   *\n   * @remarks\n   * Prefetching is only enabled in production.\n   *\n   * - In the **App Router**:\n   *   - `null` (default): Prefetch behavior depends on static vs dynamic routes:\n   *     - Static routes: fully prefetched\n   *     - Dynamic routes: partial prefetch to the nearest segment with a `loading.js`\n   *   - `true`: Always prefetch the full route and data.\n   *   - `false`: Disable prefetching on both viewport and hover.\n   * - In the **Pages Router**:\n   *   - `true` (default): Prefetches the route and data in the background on viewport or hover.\n   *   - `false`: Prefetch only on hover, not on viewport.\n   *\n   * @defaultValue `true` (Pages Router) or `null` (App Router)\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/dashboard\" prefetch={false}>\n   *   Dashboard\n   * </Link>\n   * ```\n   */\n  prefetch?: boolean | null\n\n  /**\n   * (unstable) Switch to a dynamic prefetch on hover. Effectively the same as\n   * updating the prefetch prop to `true` in a mouse event.\n   */\n  unstable_dynamicOnHover?: boolean\n\n  /**\n   * The active locale is automatically prepended in the Pages Router. `locale` allows for providing\n   * a different locale, or can be set to `false` to opt out of automatic locale behavior.\n   *\n   * @remarks\n   * Note: locale only applies in the Pages Router and is ignored in the App Router.\n   *\n   * @example\n   * ```tsx\n   * // Use the 'fr' locale:\n   * <Link href=\"/about\" locale=\"fr\">\n   *   About (French)\n   * </Link>\n   *\n   * // Disable locale prefix:\n   * <Link href=\"/about\" locale={false}>\n   *   About (no locale prefix)\n   * </Link>\n   * ```\n   */\n  locale?: string | false\n\n  /**\n   * Enable legacy link behavior, requiring an `<a>` tag to wrap the child content\n   * if the child is a string or number.\n   *\n   * @deprecated This will be removed in v16\n   * @defaultValue `false`\n   * @see https://github.com/vercel/next.js/commit/489e65ed98544e69b0afd7e0cfc3f9f6c2b803b7\n   */\n  legacyBehavior?: boolean\n\n  /**\n   * Optional event handler for when the mouse pointer is moved onto the `<Link>`.\n   */\n  onMouseEnter?: React.MouseEventHandler<HTMLAnchorElement>\n\n  /**\n   * Optional event handler for when the `<Link>` is touched.\n   */\n  onTouchStart?: React.TouchEventHandler<HTMLAnchorElement>\n\n  /**\n   * Optional event handler for when the `<Link>` is clicked.\n   */\n  onClick?: React.MouseEventHandler<HTMLAnchorElement>\n\n  /**\n   * Optional event handler for when the `<Link>` is navigated.\n   */\n  onNavigate?: OnNavigateEventHandler\n}\n\n// TODO-APP: Include the full set of Anchor props\n// adding this to the publicly exported type currently breaks existing apps\n\n// `RouteInferType` is a stub here to avoid breaking `typedRoutes` when the type\n// isn't generated yet. It will be replaced when the webpack plugin runs.\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nexport type LinkProps<RouteInferType = any> = InternalLinkProps\ntype LinkPropsRequired = RequiredKeys<LinkProps>\ntype LinkPropsOptional = OptionalKeys<Omit<InternalLinkProps, 'locale'>>\n\nfunction isModifiedEvent(event: React.MouseEvent): boolean {\n  const eventTarget = event.currentTarget as HTMLAnchorElement | SVGAElement\n  const target = eventTarget.getAttribute('target')\n  return (\n    (target && target !== '_self') ||\n    event.metaKey ||\n    event.ctrlKey ||\n    event.shiftKey ||\n    event.altKey || // triggers resource download\n    (event.nativeEvent && event.nativeEvent.which === 2)\n  )\n}\n\nfunction linkClicked(\n  e: React.MouseEvent,\n  href: string,\n  as: string,\n  linkInstanceRef: React.RefObject<LinkInstance | null>,\n  replace?: boolean,\n  scroll?: boolean,\n  onNavigate?: OnNavigateEventHandler\n): void {\n  const { nodeName } = e.currentTarget\n\n  // anchors inside an svg have a lowercase nodeName\n  const isAnchorNodeName = nodeName.toUpperCase() === 'A'\n\n  if (\n    (isAnchorNodeName && isModifiedEvent(e)) ||\n    e.currentTarget.hasAttribute('download')\n  ) {\n    // ignore click for browser’s default behavior\n    return\n  }\n\n  if (!isLocalURL(href)) {\n    if (replace) {\n      // browser default behavior does not replace the history state\n      // so we need to do it manually\n      e.preventDefault()\n      location.replace(href)\n    }\n\n    // ignore click for browser’s default behavior\n    return\n  }\n\n  e.preventDefault()\n\n  const navigate = () => {\n    if (onNavigate) {\n      let isDefaultPrevented = false\n\n      onNavigate({\n        preventDefault: () => {\n          isDefaultPrevented = true\n        },\n      })\n\n      if (isDefaultPrevented) {\n        return\n      }\n    }\n\n    dispatchNavigateAction(\n      as || href,\n      replace ? 'replace' : 'push',\n      scroll ?? true,\n      linkInstanceRef.current\n    )\n  }\n\n  React.startTransition(navigate)\n}\n\nfunction formatStringOrUrl(urlObjOrString: UrlObject | string): string {\n  if (typeof urlObjOrString === 'string') {\n    return urlObjOrString\n  }\n\n  return formatUrl(urlObjOrString)\n}\n\n/**\n * A React component that extends the HTML `<a>` element to provide\n * [prefetching](https://nextjs.org/docs/app/building-your-application/routing/linking-and-navigating#2-prefetching)\n * and client-side navigation. This is the primary way to navigate between routes in Next.js.\n *\n * @remarks\n * - Prefetching is only enabled in production.\n *\n * @see https://nextjs.org/docs/app/api-reference/components/link\n */\nexport default function LinkComponent(\n  props: LinkProps & {\n    children: React.ReactNode\n    ref: React.Ref<HTMLAnchorElement>\n  }\n) {\n  const [linkStatus, setOptimisticLinkStatus] = useOptimistic(IDLE_LINK_STATUS)\n\n  let children: React.ReactNode\n\n  const linkInstanceRef = useRef<LinkInstance | null>(null)\n\n  const {\n    href: hrefProp,\n    as: asProp,\n    children: childrenProp,\n    prefetch: prefetchProp = null,\n    passHref,\n    replace,\n    shallow,\n    scroll,\n    onClick,\n    onMouseEnter: onMouseEnterProp,\n    onTouchStart: onTouchStartProp,\n    legacyBehavior = false,\n    onNavigate,\n    ref: forwardedRef,\n    unstable_dynamicOnHover,\n    ...restProps\n  } = props\n\n  children = childrenProp\n\n  if (\n    legacyBehavior &&\n    (typeof children === 'string' || typeof children === 'number')\n  ) {\n    children = <a>{children}</a>\n  }\n\n  const router = React.useContext(AppRouterContext)\n\n  const prefetchEnabled = prefetchProp !== false\n  /**\n   * The possible states for prefetch are:\n   * - null: this is the default \"auto\" mode, where we will prefetch partially if the link is in the viewport\n   * - true: we will prefetch if the link is visible and prefetch the full page, not just partially\n   * - false: we will not prefetch if in the viewport at all\n   * - 'unstable_dynamicOnHover': this starts in \"auto\" mode, but switches to \"full\" when the link is hovered\n   */\n  const appPrefetchKind =\n    prefetchProp === null ? PrefetchKind.AUTO : PrefetchKind.FULL\n\n  if (process.env.NODE_ENV !== 'production') {\n    function createPropError(args: {\n      key: string\n      expected: string\n      actual: string\n    }) {\n      return new Error(\n        `Failed prop type: The prop \\`${args.key}\\` expects a ${args.expected} in \\`<Link>\\`, but got \\`${args.actual}\\` instead.` +\n          (typeof window !== 'undefined'\n            ? \"\\nOpen your browser's console to view the Component stack trace.\"\n            : '')\n      )\n    }\n\n    // TypeScript trick for type-guarding:\n    const requiredPropsGuard: Record<LinkPropsRequired, true> = {\n      href: true,\n    } as const\n    const requiredProps: LinkPropsRequired[] = Object.keys(\n      requiredPropsGuard\n    ) as LinkPropsRequired[]\n    requiredProps.forEach((key: LinkPropsRequired) => {\n      if (key === 'href') {\n        if (\n          props[key] == null ||\n          (typeof props[key] !== 'string' && typeof props[key] !== 'object')\n        ) {\n          throw createPropError({\n            key,\n            expected: '`string` or `object`',\n            actual: props[key] === null ? 'null' : typeof props[key],\n          })\n        }\n      } else {\n        // TypeScript trick for type-guarding:\n        // eslint-disable-next-line @typescript-eslint/no-unused-vars\n        const _: never = key\n      }\n    })\n\n    // TypeScript trick for type-guarding:\n    const optionalPropsGuard: Record<LinkPropsOptional, true> = {\n      as: true,\n      replace: true,\n      scroll: true,\n      shallow: true,\n      passHref: true,\n      prefetch: true,\n      unstable_dynamicOnHover: true,\n      onClick: true,\n      onMouseEnter: true,\n      onTouchStart: true,\n      legacyBehavior: true,\n      onNavigate: true,\n    } as const\n    const optionalProps: LinkPropsOptional[] = Object.keys(\n      optionalPropsGuard\n    ) as LinkPropsOptional[]\n    optionalProps.forEach((key: LinkPropsOptional) => {\n      const valType = typeof props[key]\n\n      if (key === 'as') {\n        if (props[key] && valType !== 'string' && valType !== 'object') {\n          throw createPropError({\n            key,\n            expected: '`string` or `object`',\n            actual: valType,\n          })\n        }\n      } else if (\n        key === 'onClick' ||\n        key === 'onMouseEnter' ||\n        key === 'onTouchStart' ||\n        key === 'onNavigate'\n      ) {\n        if (props[key] && valType !== 'function') {\n          throw createPropError({\n            key,\n            expected: '`function`',\n            actual: valType,\n          })\n        }\n      } else if (\n        key === 'replace' ||\n        key === 'scroll' ||\n        key === 'shallow' ||\n        key === 'passHref' ||\n        key === 'prefetch' ||\n        key === 'legacyBehavior' ||\n        key === 'unstable_dynamicOnHover'\n      ) {\n        if (props[key] != null && valType !== 'boolean') {\n          throw createPropError({\n            key,\n            expected: '`boolean`',\n            actual: valType,\n          })\n        }\n      } else {\n        // TypeScript trick for type-guarding:\n        // eslint-disable-next-line @typescript-eslint/no-unused-vars\n        const _: never = key\n      }\n    })\n  }\n\n  if (process.env.NODE_ENV !== 'production') {\n    if (props.locale) {\n      warnOnce(\n        'The `locale` prop is not supported in `next/link` while using the `app` router. Read more about app router internalization: https://nextjs.org/docs/app/building-your-application/routing/internationalization'\n      )\n    }\n    if (!asProp) {\n      let href: string | undefined\n      if (typeof hrefProp === 'string') {\n        href = hrefProp\n      } else if (\n        typeof hrefProp === 'object' &&\n        typeof hrefProp.pathname === 'string'\n      ) {\n        href = hrefProp.pathname\n      }\n\n      if (href) {\n        const hasDynamicSegment = href\n          .split('/')\n          .some((segment) => segment.startsWith('[') && segment.endsWith(']'))\n\n        if (hasDynamicSegment) {\n          throw new Error(\n            `Dynamic href \\`${href}\\` found in <Link> while using the \\`/app\\` router, this is not supported. Read more: https://nextjs.org/docs/messages/app-dir-dynamic-href`\n          )\n        }\n      }\n    }\n  }\n\n  const { href, as } = React.useMemo(() => {\n    const resolvedHref = formatStringOrUrl(hrefProp)\n    return {\n      href: resolvedHref,\n      as: asProp ? formatStringOrUrl(asProp) : resolvedHref,\n    }\n  }, [hrefProp, asProp])\n\n  // This will return the first child, if multiple are provided it will throw an error\n  let child: any\n  if (legacyBehavior) {\n    if (process.env.NODE_ENV === 'development') {\n      if (onClick) {\n        console.warn(\n          `\"onClick\" was passed to <Link> with \\`href\\` of \\`${hrefProp}\\` but \"legacyBehavior\" was set. The legacy behavior requires onClick be set on the child of next/link`\n        )\n      }\n      if (onMouseEnterProp) {\n        console.warn(\n          `\"onMouseEnter\" was passed to <Link> with \\`href\\` of \\`${hrefProp}\\` but \"legacyBehavior\" was set. The legacy behavior requires onMouseEnter be set on the child of next/link`\n        )\n      }\n      try {\n        child = React.Children.only(children)\n      } catch (err) {\n        if (!children) {\n          throw new Error(\n            `No children were passed to <Link> with \\`href\\` of \\`${hrefProp}\\` but one child is required https://nextjs.org/docs/messages/link-no-children`\n          )\n        }\n        throw new Error(\n          `Multiple children were passed to <Link> with \\`href\\` of \\`${hrefProp}\\` but only one child is supported https://nextjs.org/docs/messages/link-multiple-children` +\n            (typeof window !== 'undefined'\n              ? \" \\nOpen your browser's console to view the Component stack trace.\"\n              : '')\n        )\n      }\n    } else {\n      child = React.Children.only(children)\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if ((children as any)?.type === 'a') {\n        throw new Error(\n          'Invalid <Link> with <a> child. Please remove <a> or use <Link legacyBehavior>.\\nLearn more: https://nextjs.org/docs/messages/invalid-new-link-with-extra-anchor'\n        )\n      }\n    }\n  }\n\n  const childRef: any = legacyBehavior\n    ? child && typeof child === 'object' && child.ref\n    : forwardedRef\n\n  // Use a callback ref to attach an IntersectionObserver to the anchor tag on\n  // mount. In the future we will also use this to keep track of all the\n  // currently mounted <Link> instances, e.g. so we can re-prefetch them after\n  // a revalidation or refresh.\n  const observeLinkVisibilityOnMount = React.useCallback(\n    (element: HTMLAnchorElement | SVGAElement) => {\n      if (router !== null) {\n        linkInstanceRef.current = mountLinkInstance(\n          element,\n          href,\n          router,\n          appPrefetchKind,\n          prefetchEnabled,\n          setOptimisticLinkStatus\n        )\n      }\n\n      return () => {\n        if (linkInstanceRef.current) {\n          unmountLinkForCurrentNavigation(linkInstanceRef.current)\n          linkInstanceRef.current = null\n        }\n        unmountPrefetchableInstance(element)\n      }\n    },\n    [prefetchEnabled, href, router, appPrefetchKind, setOptimisticLinkStatus]\n  )\n\n  const mergedRef = useMergedRef(observeLinkVisibilityOnMount, childRef)\n\n  const childProps: {\n    onTouchStart?: React.TouchEventHandler<HTMLAnchorElement>\n    onMouseEnter: React.MouseEventHandler<HTMLAnchorElement>\n    onClick: React.MouseEventHandler<HTMLAnchorElement>\n    href?: string\n    ref?: any\n  } = {\n    ref: mergedRef,\n    onClick(e) {\n      if (process.env.NODE_ENV !== 'production') {\n        if (!e) {\n          throw new Error(\n            `Component rendered inside next/link has to pass click event to \"onClick\" prop.`\n          )\n        }\n      }\n\n      if (!legacyBehavior && typeof onClick === 'function') {\n        onClick(e)\n      }\n\n      if (\n        legacyBehavior &&\n        child.props &&\n        typeof child.props.onClick === 'function'\n      ) {\n        child.props.onClick(e)\n      }\n\n      if (!router) {\n        return\n      }\n\n      if (e.defaultPrevented) {\n        return\n      }\n\n      linkClicked(e, href, as, linkInstanceRef, replace, scroll, onNavigate)\n    },\n    onMouseEnter(e) {\n      if (!legacyBehavior && typeof onMouseEnterProp === 'function') {\n        onMouseEnterProp(e)\n      }\n\n      if (\n        legacyBehavior &&\n        child.props &&\n        typeof child.props.onMouseEnter === 'function'\n      ) {\n        child.props.onMouseEnter(e)\n      }\n\n      if (!router) {\n        return\n      }\n\n      if (!prefetchEnabled || process.env.NODE_ENV === 'development') {\n        return\n      }\n\n      const upgradeToDynamicPrefetch = unstable_dynamicOnHover === true\n      onNavigationIntent(\n        e.currentTarget as HTMLAnchorElement | SVGAElement,\n        upgradeToDynamicPrefetch\n      )\n    },\n    onTouchStart: process.env.__NEXT_LINK_NO_TOUCH_START\n      ? undefined\n      : function onTouchStart(e) {\n          if (!legacyBehavior && typeof onTouchStartProp === 'function') {\n            onTouchStartProp(e)\n          }\n\n          if (\n            legacyBehavior &&\n            child.props &&\n            typeof child.props.onTouchStart === 'function'\n          ) {\n            child.props.onTouchStart(e)\n          }\n\n          if (!router) {\n            return\n          }\n\n          if (!prefetchEnabled) {\n            return\n          }\n\n          const upgradeToDynamicPrefetch = unstable_dynamicOnHover === true\n          onNavigationIntent(\n            e.currentTarget as HTMLAnchorElement | SVGAElement,\n            upgradeToDynamicPrefetch\n          )\n        },\n  }\n\n  // If child is an <a> tag and doesn't have a href attribute, or if the 'passHref' property is\n  // defined, we specify the current 'href', so that repetition is not needed by the user.\n  // If the url is absolute, we can bypass the logic to prepend the basePath.\n  if (isAbsoluteUrl(as)) {\n    childProps.href = as\n  } else if (\n    !legacyBehavior ||\n    passHref ||\n    (child.type === 'a' && !('href' in child.props))\n  ) {\n    childProps.href = addBasePath(as)\n  }\n\n  let link: React.ReactNode\n\n  if (legacyBehavior) {\n    if (process.env.NODE_ENV === 'development') {\n      errorOnce(\n        '`legacyBehavior` is deprecated and will be removed in a future ' +\n          'release. A codemod is available to upgrade your components:\\n\\n' +\n          'npx @next/codemod@latest new-link .\\n\\n' +\n          'Learn more: https://nextjs.org/docs/app/building-your-application/upgrading/codemods#remove-a-tags-from-link-components'\n      )\n    }\n    link = React.cloneElement(child, childProps)\n  } else {\n    link = (\n      <a {...restProps} {...childProps}>\n        {children}\n      </a>\n    )\n  }\n\n  return (\n    <LinkStatusContext.Provider value={linkStatus}>\n      {link}\n    </LinkStatusContext.Provider>\n  )\n}\n\nconst LinkStatusContext = createContext<\n  typeof PENDING_LINK_STATUS | typeof IDLE_LINK_STATUS\n>(IDLE_LINK_STATUS)\n\nexport const useLinkStatus = () => {\n  return useContext(LinkStatusContext)\n}\n"], "names": ["LinkComponent", "useLinkStatus", "isModifiedEvent", "event", "eventTarget", "currentTarget", "target", "getAttribute", "metaKey", "ctrl<PERSON>ey", "shift<PERSON>ey", "altKey", "nativeEvent", "which", "linkClicked", "e", "href", "as", "linkInstanceRef", "replace", "scroll", "onNavigate", "nodeName", "isAnchorNodeName", "toUpperCase", "hasAttribute", "isLocalURL", "preventDefault", "location", "navigate", "isDefaultPrevented", "dispatchNavigateAction", "current", "React", "startTransition", "formatStringOrUrl", "urlObjOrString", "formatUrl", "props", "linkStatus", "setOptimisticLinkStatus", "useOptimistic", "IDLE_LINK_STATUS", "children", "useRef", "hrefProp", "asProp", "childrenProp", "prefetch", "prefetchProp", "passHref", "shallow", "onClick", "onMouseEnter", "onMouseEnterProp", "onTouchStart", "onTouchStartProp", "legacyBeh<PERSON>or", "ref", "forwardedRef", "unstable_dynamicOnHover", "restProps", "a", "router", "useContext", "AppRouterContext", "prefetchEnabled", "appPrefetchKind", "PrefetchKind", "AUTO", "FULL", "process", "env", "NODE_ENV", "createPropError", "args", "Error", "key", "expected", "actual", "window", "requiredPropsGuard", "requiredProps", "Object", "keys", "for<PERSON>ach", "_", "optionalPropsGuard", "optionalProps", "valType", "locale", "warnOnce", "pathname", "hasDynamicSegment", "split", "some", "segment", "startsWith", "endsWith", "useMemo", "resolvedHref", "child", "console", "warn", "Children", "only", "err", "type", "childRef", "observeLinkVisibilityOnMount", "useCallback", "element", "mountLinkInstance", "unmountLinkForCurrentNavigation", "unmountPrefetchableInstance", "mergedRef", "useMergedRef", "childProps", "defaultPrevented", "upgradeToDynamicPrefetch", "onNavigationIntent", "__NEXT_LINK_NO_TOUCH_START", "undefined", "isAbsoluteUrl", "addBasePath", "link", "errorOnce", "cloneElement", "LinkStatusContext", "Provider", "value", "createContext"], "mappings": "", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1285, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Dev2/hassona2/nhm-group-website/node_modules/next/dist/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1323, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Dev2/hassona2/nhm-group-website/node_modules/next/dist/src/build/templates/app-page.ts"], "sourcesContent": ["import type { LoaderTree } from '../../server/lib/app-dir-module'\nimport { AppPageRouteModule } from '../../server/route-modules/app-page/module.compiled' with { 'turbopack-transition': 'next-ssr' }\nimport { RouteKind } from '../../server/route-kind' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\n\n/**\n * The tree created in next-app-loader that holds component segments and modules\n * and I've updated it.\n */\ndeclare const tree: LoaderTree\ndeclare const pages: any\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\n// INJECT:tree\n// INJECT:pages\n\nexport { tree, pages }\n\nexport { default as GlobalError } from 'VAR_MODULE_GLOBAL_ERROR' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\ndeclare const __next_app_require__: (id: string | number) => unknown\ndeclare const __next_app_load_chunk__: (id: string | number) => Promise<unknown>\n\n// INJECT:__next_app_require__\n// INJECT:__next_app_load_chunk__\n\nexport const __next_app__ = {\n  require: __next_app_require__,\n  loadChunk: __next_app_load_chunk__,\n}\n\nexport * from '../../server/app-render/entry-base' with { 'turbopack-transition': 'next-server-utility' }\n\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n  definition: {\n    kind: RouteKind.APP_PAGE,\n    page: 'VAR_DEFINITION_PAGE',\n    pathname: 'VAR_DEFINITION_PATHNAME',\n    // The following aren't used in production.\n    bundlePath: '',\n    filename: '',\n    appPaths: [],\n  },\n  userland: {\n    loaderTree: tree,\n  },\n})\n"], "names": ["AppPageRouteModule", "RouteKind", "tree", "pages", "default", "GlobalError", "__next_app__", "require", "__next_app_require__", "loadChunk", "__next_app_load_chunk__", "routeModule", "definition", "kind", "APP_PAGE", "page", "pathname", "bundlePath", "filename", "appPaths", "userland", "loaderTree"], "mappings": ";;;;;;AACA,SAASA,kBAAkB,QAAQ,2DAA2D;IAAE,wBAAwB;AAAW,EAAC;IACzE,wBAAwB;AAWnF,yEAAyE;AAEzE,cAAc;AAGd,SAASE,IAAI,EAAEC,KAAK,GAAE;IAEkD,wBAAwB;AAOhG,iCAAiC;;;;;;;;;;;;IAI/BM,WAAWC,0DAAAA;AACb,EAAC,QAAA;AAED,MAAA,OAAA;IAAc;IAAA,sCAA0C;YAAE,QAAA;YAAA;YAAA,EAAwB;gBAAsB,EAAC,UAAA;oBAAA;oBAAA,CAEzG;oBAAA,yDAA4D;wBAC5D,KAAO,KAAA,CAAMC;wBAAAA,QAAc;4BAAA,GAAIX,CAAAA,gBAAmB;4BAAA;yBAAA;;mBAChDY,YAAY;;iBACVC,MAAMZ,UAAUa,QAAQ;sBACxBC,IAAAA,CAAM,CAAA;YAAA;SAAA;;SACNC,UAAU;cACV,IAAA;YAAA,MAAA,4BAA2C;iBAC3CC,MAAAA,MAAY,EAAA;wBAAA;4BACZC,KAAAA,CAAAA,GAAAA,EAAU,0MAAVA,CAAAA,sBAAU,EAAA,MAAA,MAAA,MAAA,MAAA,EAAA,iBAAA,CAAA,CAAA,EAAA,6SAAA,CAAA,UAAA,CAAA,GAAA,CAAA,KAAA,CAAA,KAAA,MAAA,CAAA,CAAA,EAAA,CAAA,EAAA,EAAA;4BACVC,OAAAA,iTAAU,EAAE,QAAA,CAAA,KAAA,CAAA,CAAA,EAAA,6SAAA,CAAA,UAAA,CAAA,MAAA,EAAA;4BACd,MAAA,CAAA,YAAA,CAAA;wBACAC;qBAAAA,MAAU;gBACRC,YAAYnB;UACd;QAAA,UAAA;YAAA,IAAA;YAAA;SAAA;QACF,CAAE,YAAA;YAAA,IAAA;YAAA;SAAA", "ignoreList": [0], "debugId": null}}]}