import Image from "next/image";
import Link from "next/link";

export default function About() {
  return (
    <div className="min-h-screen">
      {/* Navigation */}
      <nav className="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Link href="/">
                <h1 className="text-2xl font-bold text-blue-900 font-serif">NHM Group Holdings</h1>
              </Link>
            </div>
            <div className="hidden md:block">
              <div className="ml-10 flex items-baseline space-x-8">
                <Link href="/" className="text-gray-600 hover:text-yellow-600 transition-colors px-3 py-2 text-sm font-medium">
                  Home
                </Link>
                <Link href="/about" className="text-blue-900 hover:text-yellow-600 transition-colors px-3 py-2 text-sm font-medium">
                  About Us
                </Link>
                <Link href="/companies" className="text-gray-600 hover:text-yellow-600 transition-colors px-3 py-2 text-sm font-medium">
                  Our Companies
                </Link>
                <Link href="/clients" className="text-gray-600 hover:text-yellow-600 transition-colors px-3 py-2 text-sm font-medium">
                  Clients & Partners
                </Link>
                <Link href="/contact" className="text-gray-600 hover:text-yellow-600 transition-colors px-3 py-2 text-sm font-medium">
                  Contact
                </Link>
              </div>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="bg-gradient-to-r from-blue-900 to-blue-700 text-white py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-5xl font-bold font-serif mb-6">About NHM Group Holdings</h1>
          <p className="text-xl text-blue-100 max-w-3xl mx-auto">
            A legacy of excellence, innovation, and strategic growth across diverse industries
          </p>
        </div>
      </section>

      {/* Chairman Profile */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div className="order-2 lg:order-1">
              <h2 className="text-4xl font-bold font-serif text-blue-900 mb-6">Chairman's Message</h2>
              <h3 className="text-2xl font-semibold text-gray-700 mb-4">Nasr Hassan Mohamed</h3>
              <p className="text-lg text-gray-600 mb-6 leading-relaxed">
                With over three decades of experience in business leadership and strategic development, 
                I have had the privilege of building NHM Group Holdings into a diversified portfolio 
                of successful enterprises across multiple industries.
              </p>
              <p className="text-lg text-gray-600 mb-6 leading-relaxed">
                Our journey began with a simple vision: to create sustainable value through strategic 
                investments and operational excellence. Today, NHM Group Holdings stands as a testament 
                to the power of visionary leadership, strategic partnerships, and unwavering commitment 
                to excellence.
              </p>
              <p className="text-lg text-gray-600 mb-6 leading-relaxed">
                From our flagship automotive operations, including our successful ISUZU dealership in Libya, 
                to our expanding portfolio in technology and real estate, we continue to identify and 
                capitalize on opportunities that drive growth and create value for all stakeholders.
              </p>
              <div className="bg-yellow-50 border-l-4 border-yellow-400 p-6 mt-8">
                <p className="text-lg italic text-gray-700">
                  "Our success is measured not just by financial returns, but by the positive impact 
                  we create in the communities we serve and the lasting partnerships we build."
                </p>
                <p className="text-right text-gray-600 mt-4 font-semibold">- Nasr Hassan Mohamed</p>
              </div>
            </div>
            <div className="order-1 lg:order-2">
              <div className="relative">
                <div className="bg-gradient-to-br from-blue-100 to-blue-50 rounded-2xl p-8">
                  <Image
                    src="/nasr1.jpg"
                    alt="Nasr Hassan Mohamed - Chairman of NHM Group Holdings"
                    width={500}
                    height={600}
                    className="rounded-lg shadow-xl"
                    priority
                  />
                </div>
                <div className="absolute -bottom-6 -right-6 bg-yellow-400 text-blue-900 p-4 rounded-lg shadow-lg">
                  <p className="font-bold text-lg">30+ Years</p>
                  <p className="text-sm">Business Leadership</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Company Background */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold font-serif text-blue-900 mb-4">Our Story</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Building a legacy of success through strategic vision and operational excellence
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="bg-white rounded-xl p-8 shadow-lg">
              <div className="w-16 h-16 bg-blue-900 rounded-lg flex items-center justify-center mb-6">
                <span className="text-white text-2xl font-bold">1</span>
              </div>
              <h3 className="text-xl font-bold text-blue-900 mb-4">Foundation & Vision</h3>
              <p className="text-gray-600">
                NHM Group Holdings was founded with a clear vision to create a diversified portfolio 
                of businesses that would drive sustainable growth and create lasting value.
              </p>
            </div>

            <div className="bg-white rounded-xl p-8 shadow-lg">
              <div className="w-16 h-16 bg-yellow-400 rounded-lg flex items-center justify-center mb-6">
                <span className="text-blue-900 text-2xl font-bold">2</span>
              </div>
              <h3 className="text-xl font-bold text-blue-900 mb-4">Strategic Growth</h3>
              <p className="text-gray-600">
                Through strategic acquisitions and partnerships, we expanded our portfolio across 
                automotive, technology, and real estate sectors, establishing market leadership positions.
              </p>
            </div>

            <div className="bg-white rounded-xl p-8 shadow-lg">
              <div className="w-16 h-16 bg-gray-600 rounded-lg flex items-center justify-center mb-6">
                <span className="text-white text-2xl font-bold">3</span>
              </div>
              <h3 className="text-xl font-bold text-blue-900 mb-4">Future Innovation</h3>
              <p className="text-gray-600">
                Today, we continue to innovate and expand, identifying new opportunities and 
                technologies that will drive the next phase of our growth journey.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Values Section */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold font-serif text-blue-900 mb-4">Our Values</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              The principles that guide our decisions and shape our culture
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="w-20 h-20 bg-blue-900 rounded-full flex items-center justify-center mx-auto mb-6">
                <span className="text-white text-3xl">🎯</span>
              </div>
              <h3 className="text-xl font-bold text-blue-900 mb-4">Excellence</h3>
              <p className="text-gray-600">
                We strive for excellence in everything we do, setting high standards and 
                continuously improving our performance.
              </p>
            </div>

            <div className="text-center">
              <div className="w-20 h-20 bg-yellow-400 rounded-full flex items-center justify-center mx-auto mb-6">
                <span className="text-blue-900 text-3xl">🤝</span>
              </div>
              <h3 className="text-xl font-bold text-blue-900 mb-4">Integrity</h3>
              <p className="text-gray-600">
                We conduct business with the highest ethical standards, building trust through 
                transparency and accountability.
              </p>
            </div>

            <div className="text-center">
              <div className="w-20 h-20 bg-gray-600 rounded-full flex items-center justify-center mx-auto mb-6">
                <span className="text-white text-3xl">💡</span>
              </div>
              <h3 className="text-xl font-bold text-blue-900 mb-4">Innovation</h3>
              <p className="text-gray-600">
                We embrace innovation and new technologies to stay ahead of market trends and 
                create competitive advantages.
              </p>
            </div>

            <div className="text-center">
              <div className="w-20 h-20 bg-green-600 rounded-full flex items-center justify-center mx-auto mb-6">
                <span className="text-white text-3xl">🌱</span>
              </div>
              <h3 className="text-xl font-bold text-blue-900 mb-4">Sustainability</h3>
              <p className="text-gray-600">
                We are committed to sustainable business practices that benefit our stakeholders 
                and the communities we serve.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="bg-gradient-to-r from-blue-900 to-blue-700 text-white py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-4xl font-bold font-serif mb-6">Join Our Journey</h2>
          <p className="text-xl mb-8 text-blue-100 max-w-3xl mx-auto">
            Discover how NHM Group Holdings can be your partner in success
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link 
              href="/companies" 
              className="bg-yellow-400 text-blue-900 px-8 py-3 rounded-lg font-semibold hover:bg-yellow-300 transition-colors"
            >
              Explore Our Portfolio
            </Link>
            <Link 
              href="/contact" 
              className="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-blue-900 transition-colors"
            >
              Get In Touch
            </Link>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-blue-900 text-white py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="col-span-1 md:col-span-2">
              <h3 className="text-2xl font-bold font-serif mb-4">NHM Group Holdings</h3>
              <p className="text-blue-200 mb-4">
                Building tomorrow's success through strategic investments and operational excellence.
              </p>
            </div>
            <div>
              <h4 className="text-lg font-semibold mb-4">Quick Links</h4>
              <ul className="space-y-2">
                <li><Link href="/about" className="text-blue-200 hover:text-yellow-400 transition-colors">About Us</Link></li>
                <li><Link href="/companies" className="text-blue-200 hover:text-yellow-400 transition-colors">Our Companies</Link></li>
                <li><Link href="/clients" className="text-blue-200 hover:text-yellow-400 transition-colors">Clients & Partners</Link></li>
                <li><Link href="/contact" className="text-blue-200 hover:text-yellow-400 transition-colors">Contact</Link></li>
              </ul>
            </div>
            <div>
              <h4 className="text-lg font-semibold mb-4">Contact Info</h4>
              <p className="text-blue-200 mb-2">Email: <EMAIL></p>
              <p className="text-blue-200 mb-2">Phone: +****************</p>
            </div>
          </div>
          <div className="border-t border-blue-800 mt-8 pt-8 text-center">
            <p className="text-blue-200">&copy; 2024 NHM Group Holdings. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}
