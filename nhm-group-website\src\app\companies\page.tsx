import Image from "next/image";
import Link from "next/link";

export default function Companies() {
  return (
    <div className="min-h-screen">
      {/* Navigation */}
      <nav className="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Link href="/">
                <h1 className="text-2xl font-bold text-blue-900 font-serif">NHM Group Holdings</h1>
              </Link>
            </div>
            <div className="hidden md:block">
              <div className="ml-10 flex items-baseline space-x-8">
                <Link href="/" className="text-gray-600 hover:text-yellow-600 transition-colors px-3 py-2 text-sm font-medium">
                  Home
                </Link>
                <Link href="/about" className="text-gray-600 hover:text-yellow-600 transition-colors px-3 py-2 text-sm font-medium">
                  About Us
                </Link>
                <Link href="/companies" className="text-blue-900 hover:text-yellow-600 transition-colors px-3 py-2 text-sm font-medium">
                  Our Companies
                </Link>
                <Link href="/clients" className="text-gray-600 hover:text-yellow-600 transition-colors px-3 py-2 text-sm font-medium">
                  Clients & Partners
                </Link>
                <Link href="/contact" className="text-gray-600 hover:text-yellow-600 transition-colors px-3 py-2 text-sm font-medium">
                  Contact
                </Link>
              </div>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="bg-gradient-to-r from-blue-900 to-blue-700 text-white py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-5xl font-bold font-serif mb-6">Our Portfolio</h1>
          <p className="text-xl text-blue-100 max-w-3xl mx-auto">
            Discover our diverse range of companies spanning multiple industries and markets
          </p>
        </div>
      </section>

      {/* Portfolio Overview */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold font-serif text-blue-900 mb-4">Diversified Excellence</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Our strategic investments across key sectors drive sustainable growth and create lasting value
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {/* Automotive Division */}
            <Link href="/companies/automotive" className="group">
              <div className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 group-hover:scale-105">
                <div className="h-48 bg-gradient-to-br from-blue-900 to-blue-600 relative overflow-hidden">
                  <div className="absolute inset-0 bg-black bg-opacity-20"></div>
                  <div className="absolute bottom-4 left-4 text-white">
                    <h3 className="text-2xl font-bold">Automotive</h3>
                    <p className="text-blue-100">Leading dealerships & services</p>
                  </div>
                </div>
                <div className="p-6">
                  <h3 className="text-xl font-bold text-blue-900 mb-2">Automotive Division</h3>
                  <p className="text-gray-600 mb-4">
                    Leading automotive dealerships and services, including our flagship ISUZU operations in Libya.
                  </p>
                  <div className="flex items-center text-yellow-600 font-semibold group-hover:text-yellow-700">
                    Learn More 
                    <span className="ml-2 transform group-hover:translate-x-1 transition-transform">→</span>
                  </div>
                </div>
              </div>
            </Link>

            {/* Technology Solutions */}
            <Link href="/companies/technology" className="group">
              <div className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 group-hover:scale-105">
                <div className="h-48 bg-gradient-to-br from-yellow-400 to-yellow-500 relative overflow-hidden">
                  <div className="absolute inset-0 bg-black bg-opacity-20"></div>
                  <div className="absolute bottom-4 left-4 text-white">
                    <h3 className="text-2xl font-bold">Technology</h3>
                    <p className="text-yellow-100">Innovation & digital solutions</p>
                  </div>
                </div>
                <div className="p-6">
                  <h3 className="text-xl font-bold text-blue-900 mb-2">Technology Solutions</h3>
                  <p className="text-gray-600 mb-4">
                    Innovative technology companies driving digital transformation across various sectors.
                  </p>
                  <div className="flex items-center text-yellow-600 font-semibold group-hover:text-yellow-700">
                    Learn More 
                    <span className="ml-2 transform group-hover:translate-x-1 transition-transform">→</span>
                  </div>
                </div>
              </div>
            </Link>

            {/* Real Estate */}
            <Link href="/companies/real-estate" className="group">
              <div className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 group-hover:scale-105">
                <div className="h-48 bg-gradient-to-br from-gray-600 to-gray-800 relative overflow-hidden">
                  <div className="absolute inset-0 bg-black bg-opacity-20"></div>
                  <div className="absolute bottom-4 left-4 text-white">
                    <h3 className="text-2xl font-bold">Real Estate</h3>
                    <p className="text-gray-100">Strategic investments & development</p>
                  </div>
                </div>
                <div className="p-6">
                  <h3 className="text-xl font-bold text-blue-900 mb-2">Real Estate</h3>
                  <p className="text-gray-600 mb-4">
                    Strategic real estate investments and development projects in key markets.
                  </p>
                  <div className="flex items-center text-yellow-600 font-semibold group-hover:text-yellow-700">
                    Learn More 
                    <span className="ml-2 transform group-hover:translate-x-1 transition-transform">→</span>
                  </div>
                </div>
              </div>
            </Link>

            {/* Manufacturing */}
            <Link href="/companies/manufacturing" className="group">
              <div className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 group-hover:scale-105">
                <div className="h-48 bg-gradient-to-br from-green-600 to-green-700 relative overflow-hidden">
                  <div className="absolute inset-0 bg-black bg-opacity-20"></div>
                  <div className="absolute bottom-4 left-4 text-white">
                    <h3 className="text-2xl font-bold">Manufacturing</h3>
                    <p className="text-green-100">Industrial excellence</p>
                  </div>
                </div>
                <div className="p-6">
                  <h3 className="text-xl font-bold text-blue-900 mb-2">Manufacturing</h3>
                  <p className="text-gray-600 mb-4">
                    Advanced manufacturing facilities producing high-quality products for global markets.
                  </p>
                  <div className="flex items-center text-yellow-600 font-semibold group-hover:text-yellow-700">
                    Learn More 
                    <span className="ml-2 transform group-hover:translate-x-1 transition-transform">→</span>
                  </div>
                </div>
              </div>
            </Link>

            {/* Trading & Distribution */}
            <Link href="/companies/trading" className="group">
              <div className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 group-hover:scale-105">
                <div className="h-48 bg-gradient-to-br from-purple-600 to-purple-700 relative overflow-hidden">
                  <div className="absolute inset-0 bg-black bg-opacity-20"></div>
                  <div className="absolute bottom-4 left-4 text-white">
                    <h3 className="text-2xl font-bold">Trading</h3>
                    <p className="text-purple-100">Global distribution network</p>
                  </div>
                </div>
                <div className="p-6">
                  <h3 className="text-xl font-bold text-blue-900 mb-2">Trading & Distribution</h3>
                  <p className="text-gray-600 mb-4">
                    Comprehensive trading and distribution services connecting markets across regions.
                  </p>
                  <div className="flex items-center text-yellow-600 font-semibold group-hover:text-yellow-700">
                    Learn More 
                    <span className="ml-2 transform group-hover:translate-x-1 transition-transform">→</span>
                  </div>
                </div>
              </div>
            </Link>

            {/* Financial Services */}
            <Link href="/companies/financial" className="group">
              <div className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 group-hover:scale-105">
                <div className="h-48 bg-gradient-to-br from-indigo-600 to-indigo-700 relative overflow-hidden">
                  <div className="absolute inset-0 bg-black bg-opacity-20"></div>
                  <div className="absolute bottom-4 left-4 text-white">
                    <h3 className="text-2xl font-bold">Financial</h3>
                    <p className="text-indigo-100">Investment & advisory services</p>
                  </div>
                </div>
                <div className="p-6">
                  <h3 className="text-xl font-bold text-blue-900 mb-2">Financial Services</h3>
                  <p className="text-gray-600 mb-4">
                    Comprehensive financial services including investment management and advisory solutions.
                  </p>
                  <div className="flex items-center text-yellow-600 font-semibold group-hover:text-yellow-700">
                    Learn More 
                    <span className="ml-2 transform group-hover:translate-x-1 transition-transform">→</span>
                  </div>
                </div>
              </div>
            </Link>
          </div>
        </div>
      </section>

      {/* Performance Metrics */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold font-serif text-blue-900 mb-4">Portfolio Performance</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Our companies deliver consistent growth and value creation across all sectors
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="text-4xl font-bold text-blue-900 mb-2">15+</div>
              <div className="text-gray-600">Portfolio Companies</div>
            </div>
            <div className="text-center">
              <div className="text-4xl font-bold text-yellow-600 mb-2">8</div>
              <div className="text-gray-600">Industry Sectors</div>
            </div>
            <div className="text-center">
              <div className="text-4xl font-bold text-green-600 mb-2">25+</div>
              <div className="text-gray-600">Years Experience</div>
            </div>
            <div className="text-center">
              <div className="text-4xl font-bold text-purple-600 mb-2">5</div>
              <div className="text-gray-600">Countries</div>
            </div>
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="bg-gradient-to-r from-blue-900 to-blue-700 text-white py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-4xl font-bold font-serif mb-6">Explore Investment Opportunities</h2>
          <p className="text-xl mb-8 text-blue-100 max-w-3xl mx-auto">
            Discover how our diverse portfolio can create value for your investment goals
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link 
              href="/contact" 
              className="bg-yellow-400 text-blue-900 px-8 py-3 rounded-lg font-semibold hover:bg-yellow-300 transition-colors"
            >
              Contact Our Team
            </Link>
            <Link 
              href="/clients" 
              className="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-blue-900 transition-colors"
            >
              View Our Partners
            </Link>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-blue-900 text-white py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="col-span-1 md:col-span-2">
              <h3 className="text-2xl font-bold font-serif mb-4">NHM Group Holdings</h3>
              <p className="text-blue-200 mb-4">
                Building tomorrow's success through strategic investments and operational excellence.
              </p>
            </div>
            <div>
              <h4 className="text-lg font-semibold mb-4">Quick Links</h4>
              <ul className="space-y-2">
                <li><Link href="/about" className="text-blue-200 hover:text-yellow-400 transition-colors">About Us</Link></li>
                <li><Link href="/companies" className="text-blue-200 hover:text-yellow-400 transition-colors">Our Companies</Link></li>
                <li><Link href="/clients" className="text-blue-200 hover:text-yellow-400 transition-colors">Clients & Partners</Link></li>
                <li><Link href="/contact" className="text-blue-200 hover:text-yellow-400 transition-colors">Contact</Link></li>
              </ul>
            </div>
            <div>
              <h4 className="text-lg font-semibold mb-4">Contact Info</h4>
              <p className="text-blue-200 mb-2">Email: <EMAIL></p>
              <p className="text-blue-200 mb-2">Phone: +****************</p>
            </div>
          </div>
          <div className="border-t border-blue-800 mt-8 pt-8 text-center">
            <p className="text-blue-200">&copy; 2024 NHM Group Holdings. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}
