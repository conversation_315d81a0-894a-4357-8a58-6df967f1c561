import { type DevToolsInfoPropsCore } from './dev-tools-info';
import type { HTMLProps } from 'react';
export declare function TurbopackInfo(props: DevToolsInfoPropsCore & HTMLProps<HTMLDivElement>): import("react/jsx-runtime").JSX.Element;
export declare const DEV_TOOLS_INFO_TURBOPACK_INFO_STYLES = "\n  .dev-tools-info-code {\n    background: var(--color-gray-400);\n    color: var(--color-gray-1000);\n    font-family: var(--font-stack-monospace);\n    padding: 2px 4px;\n    margin: 0;\n    font-size: var(--size-13);\n    white-space: break-spaces;\n    border-radius: var(--rounded-md-2);\n  }\n\n  .dev-tools-info-code-block-container {\n    padding: 6px;\n  }\n\n  .dev-tools-info-code-block {\n    position: relative;\n    background: var(--color-background-200);\n    border: 1px solid var(--color-gray-alpha-400);\n    border-radius: var(--rounded-md-2);\n    min-width: 326px;\n  }\n\n  .dev-tools-info-code-block-pre {\n    margin: 0;\n    font-family: var(--font-stack-monospace);\n    font-size: var(--size-12);\n  }\n\n  .dev-tools-info-copy-button {\n    position: absolute;\n\n    display: flex;\n    justify-content: center;\n    align-items: center;\n    right: 8px;\n    top: 8px;\n    padding: 4px;\n    height: var(--size-24);\n    width: var(--size-24);\n    border-radius: var(--rounded-md-2);\n    border: 1px solid var(--color-gray-alpha-400);\n    background: var(--color-background-100);\n  }\n\n  .dev-tools-info-code-block-line {\n    display: block;\n    line-height: 1.5;\n    padding: 0 16px;\n  }\n\n  .dev-tools-info-code-block-line.dev-tools-info-highlight {\n    border-left: 2px solid var(--color-blue-900);\n    background: var(--color-blue-400);\n  }\n\n  .dev-tools-info-code-block-json-key {\n    color: var(--color-syntax-keyword);\n  }\n\n  .dev-tools-info-code-block-json-value {\n    color: var(--color-syntax-link);\n  }\n";
