{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Dev2/hassona2/nhm-group-website/src/app/page.tsx"], "sourcesContent": ["import Image from \"next/image\";\nimport Link from \"next/link\";\n\nexport default function Home() {\n  return (\n    <div className=\"min-h-screen\">\n      {/* Navigation */}\n      <nav className=\"bg-white shadow-sm border-b border-gray-200 sticky top-0 z-50\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center h-16\">\n            <div className=\"flex items-center\">\n              <h1 className=\"text-2xl font-bold text-navy font-serif\">NHM Group Holdings</h1>\n            </div>\n            <div className=\"hidden md:block\">\n              <div className=\"ml-10 flex items-baseline space-x-8\">\n                <Link href=\"/\" className=\"text-navy hover:text-gold transition-colors px-3 py-2 text-sm font-medium\">\n                  Home\n                </Link>\n                <Link href=\"/about\" className=\"text-gray-600 hover:text-gold transition-colors px-3 py-2 text-sm font-medium\">\n                  About Us\n                </Link>\n                <Link href=\"/companies\" className=\"text-gray-600 hover:text-gold transition-colors px-3 py-2 text-sm font-medium\">\n                  Our Companies\n                </Link>\n                <Link href=\"/clients\" className=\"text-gray-600 hover:text-gold transition-colors px-3 py-2 text-sm font-medium\">\n                  Clients & Partners\n                </Link>\n                <Link href=\"/contact\" className=\"text-gray-600 hover:text-gold transition-colors px-3 py-2 text-sm font-medium\">\n                  Contact\n                </Link>\n              </div>\n            </div>\n          </div>\n        </div>\n      </nav>\n\n      {/* Hero Section */}\n      <section className=\"hero-gradient text-white section-padding\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\">\n            <div>\n              <h1 className=\"text-5xl lg:text-6xl font-bold font-serif mb-6 leading-tight\">\n                Building Tomorrow's\n                <span className=\"gold-accent\"> Success</span>\n              </h1>\n              <p className=\"text-xl mb-8 text-blue-100 leading-relaxed\">\n                NHM Group Holdings is a diverse and visionary holding company committed to strategic growth,\n                innovation, and excellence across multiple industries. We build lasting partnerships and create\n                sustainable value for our stakeholders.\n              </p>\n              <div className=\"flex flex-col sm:flex-row gap-4\">\n                <Link\n                  href=\"/companies\"\n                  className=\"bg-gold text-navy px-8 py-3 rounded-lg font-semibold hover:bg-yellow-400 transition-colors text-center\"\n                >\n                  Explore Our Portfolio\n                </Link>\n                <Link\n                  href=\"/about\"\n                  className=\"border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-navy transition-colors text-center\"\n                >\n                  Learn About Us\n                </Link>\n              </div>\n            </div>\n            <div className=\"relative\">\n              <div className=\"bg-white/10 backdrop-blur-sm rounded-2xl p-8\">\n                <Image\n                  src=\"/image.png\"\n                  alt=\"NHM Group Holdings Business\"\n                  width={600}\n                  height={400}\n                  className=\"rounded-lg shadow-2xl\"\n                  priority\n                />\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Mission Statement */}\n      <section className=\"section-padding bg-light-gray\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n          <h2 className=\"text-4xl font-bold font-serif text-navy mb-8\">Our Mission</h2>\n          <p className=\"text-xl text-gray-700 max-w-4xl mx-auto leading-relaxed\">\n            To create sustainable value through strategic investments, operational excellence, and innovative\n            partnerships across diverse industries. We are committed to building a legacy of success that\n            benefits our stakeholders, communities, and the global economy.\n          </p>\n        </div>\n      </section>\n\n      {/* Portfolio Overview */}\n      <section className=\"section-padding\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-4xl font-bold font-serif text-navy mb-4\">Our Portfolio</h2>\n            <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n              Discover our diverse range of companies spanning automotive, technology, real estate, and more.\n            </p>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n            <div className=\"bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow\">\n              <div className=\"h-48 bg-gradient-to-br from-navy to-blue-600\"></div>\n              <div className=\"p-6\">\n                <h3 className=\"text-xl font-bold text-navy mb-2\">Automotive Division</h3>\n                <p className=\"text-gray-600 mb-4\">\n                  Leading automotive dealerships and services, including our flagship ISUZU operations in Libya.\n                </p>\n                <Link href=\"/companies/automotive\" className=\"text-gold hover:text-yellow-600 font-semibold\">\n                  Learn More →\n                </Link>\n              </div>\n            </div>\n\n            <div className=\"bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow\">\n              <div className=\"h-48 bg-gradient-to-br from-gold to-yellow-500\"></div>\n              <div className=\"p-6\">\n                <h3 className=\"text-xl font-bold text-navy mb-2\">Technology Solutions</h3>\n                <p className=\"text-gray-600 mb-4\">\n                  Innovative technology companies driving digital transformation across various sectors.\n                </p>\n                <Link href=\"/companies/technology\" className=\"text-gold hover:text-yellow-600 font-semibold\">\n                  Learn More →\n                </Link>\n              </div>\n            </div>\n\n            <div className=\"bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow\">\n              <div className=\"h-48 bg-gradient-to-br from-gray-600 to-gray-800\"></div>\n              <div className=\"p-6\">\n                <h3 className=\"text-xl font-bold text-navy mb-2\">Real Estate</h3>\n                <p className=\"text-gray-600 mb-4\">\n                  Strategic real estate investments and development projects in key markets.\n                </p>\n                <Link href=\"/companies/real-estate\" className=\"text-gold hover:text-yellow-600 font-semibold\">\n                  Learn More →\n                </Link>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Call to Action */}\n      <section className=\"hero-gradient text-white section-padding\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n          <h2 className=\"text-4xl font-bold font-serif mb-6\">Ready to Partner With Us?</h2>\n          <p className=\"text-xl mb-8 text-blue-100 max-w-3xl mx-auto\">\n            Join us in building the future. Whether you're an investor, partner, or client,\n            we're committed to creating mutual success.\n          </p>\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n            <Link\n              href=\"/contact\"\n              className=\"bg-gold text-navy px-8 py-3 rounded-lg font-semibold hover:bg-yellow-400 transition-colors\"\n            >\n              Get In Touch\n            </Link>\n            <Link\n              href=\"/companies\"\n              className=\"border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-navy transition-colors\"\n            >\n              View Our Companies\n            </Link>\n          </div>\n        </div>\n      </section>\n\n      {/* Footer */}\n      <footer className=\"bg-navy text-white py-12\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"grid grid-cols-1 md:grid-cols-4 gap-8\">\n            <div className=\"col-span-1 md:col-span-2\">\n              <h3 className=\"text-2xl font-bold font-serif mb-4\">NHM Group Holdings</h3>\n              <p className=\"text-blue-200 mb-4\">\n                Building tomorrow's success through strategic investments and operational excellence.\n              </p>\n            </div>\n            <div>\n              <h4 className=\"text-lg font-semibold mb-4\">Quick Links</h4>\n              <ul className=\"space-y-2\">\n                <li><Link href=\"/about\" className=\"text-blue-200 hover:text-gold transition-colors\">About Us</Link></li>\n                <li><Link href=\"/companies\" className=\"text-blue-200 hover:text-gold transition-colors\">Our Companies</Link></li>\n                <li><Link href=\"/clients\" className=\"text-blue-200 hover:text-gold transition-colors\">Clients & Partners</Link></li>\n                <li><Link href=\"/contact\" className=\"text-blue-200 hover:text-gold transition-colors\">Contact</Link></li>\n              </ul>\n            </div>\n            <div>\n              <h4 className=\"text-lg font-semibold mb-4\">Contact Info</h4>\n              <p className=\"text-blue-200 mb-2\">Email: <EMAIL></p>\n              <p className=\"text-blue-200 mb-2\">Phone: +****************</p>\n            </div>\n          </div>\n          <div className=\"border-t border-blue-800 mt-8 pt-8 text-center\">\n            <p className=\"text-blue-200\">&copy; 2024 NHM Group Holdings. All rights reserved.</p>\n          </div>\n        </div>\n      </footer>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAG,WAAU;8CAA0C;;;;;;;;;;;0CAE1D,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAI,WAAU;sDAA4E;;;;;;sDAGrG,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAS,WAAU;sDAAgF;;;;;;sDAG9G,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAa,WAAU;sDAAgF;;;;;;sDAGlH,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAW,WAAU;sDAAgF;;;;;;sDAGhH,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAW,WAAU;sDAAgF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAU1H,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;;4CAA+D;0DAE3E,8OAAC;gDAAK,WAAU;0DAAc;;;;;;;;;;;;kDAEhC,8OAAC;wCAAE,WAAU;kDAA6C;;;;;;kDAK1D,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;0DACX;;;;;;0DAGD,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;0DACX;;;;;;;;;;;;;;;;;;0CAKL,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;wCACJ,KAAI;wCACJ,KAAI;wCACJ,OAAO;wCACP,QAAQ;wCACR,WAAU;wCACV,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASpB,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA+C;;;;;;sCAC7D,8OAAC;4BAAE,WAAU;sCAA0D;;;;;;;;;;;;;;;;;0BAS3E,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA+C;;;;;;8CAC7D,8OAAC;oCAAE,WAAU;8CAA0C;;;;;;;;;;;;sCAKzD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAmC;;;;;;8DACjD,8OAAC;oDAAE,WAAU;8DAAqB;;;;;;8DAGlC,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAwB,WAAU;8DAAgD;;;;;;;;;;;;;;;;;;8CAMjG,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAmC;;;;;;8DACjD,8OAAC;oDAAE,WAAU;8DAAqB;;;;;;8DAGlC,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAwB,WAAU;8DAAgD;;;;;;;;;;;;;;;;;;8CAMjG,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAmC;;;;;;8DACjD,8OAAC;oDAAE,WAAU;8DAAqB;;;;;;8DAGlC,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAyB,WAAU;8DAAgD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUxG,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAqC;;;;;;sCACnD,8OAAC;4BAAE,WAAU;sCAA+C;;;;;;sCAI5D,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;0BAQP,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAqC;;;;;;sDACnD,8OAAC;4CAAE,WAAU;sDAAqB;;;;;;;;;;;;8CAIpC,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAC3C,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;8DAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAS,WAAU;kEAAkD;;;;;;;;;;;8DACpF,8OAAC;8DAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAa,WAAU;kEAAkD;;;;;;;;;;;8DACxF,8OAAC;8DAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAW,WAAU;kEAAkD;;;;;;;;;;;8DACtF,8OAAC;8DAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAW,WAAU;kEAAkD;;;;;;;;;;;;;;;;;;;;;;;8CAG1F,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAC3C,8OAAC;4CAAE,WAAU;sDAAqB;;;;;;sDAClC,8OAAC;4CAAE,WAAU;sDAAqB;;;;;;;;;;;;;;;;;;sCAGtC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMzC", "debugId": null}}]}