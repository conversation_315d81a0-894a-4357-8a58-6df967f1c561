import Image from "next/image";
import Link from "next/link";

export default function Home() {
  return (
    <div className="min-h-screen">
      {/* Navigation */}
      <nav className="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <h1 className="text-2xl font-bold text-navy font-serif">NHM Group Holdings</h1>
            </div>
            <div className="hidden md:block">
              <div className="ml-10 flex items-baseline space-x-8">
                <Link href="/" className="text-navy hover:text-gold transition-colors px-3 py-2 text-sm font-medium">
                  Home
                </Link>
                <Link href="/about" className="text-gray-600 hover:text-gold transition-colors px-3 py-2 text-sm font-medium">
                  About Us
                </Link>
                <Link href="/companies" className="text-gray-600 hover:text-gold transition-colors px-3 py-2 text-sm font-medium">
                  Our Companies
                </Link>
                <Link href="/clients" className="text-gray-600 hover:text-gold transition-colors px-3 py-2 text-sm font-medium">
                  Clients & Partners
                </Link>
                <Link href="/contact" className="text-gray-600 hover:text-gold transition-colors px-3 py-2 text-sm font-medium">
                  Contact
                </Link>
              </div>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="hero-gradient text-white section-padding">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h1 className="text-5xl lg:text-6xl font-bold font-serif mb-6 leading-tight">
                Building Tomorrow's
                <span className="gold-accent"> Success</span>
              </h1>
              <p className="text-xl mb-8 text-blue-100 leading-relaxed">
                NHM Group Holdings is a diverse and visionary holding company committed to strategic growth,
                innovation, and excellence across multiple industries. We build lasting partnerships and create
                sustainable value for our stakeholders.
              </p>
              <div className="flex flex-col sm:flex-row gap-4">
                <Link
                  href="/companies"
                  className="bg-gold text-navy px-8 py-3 rounded-lg font-semibold hover:bg-yellow-400 transition-colors text-center"
                >
                  Explore Our Portfolio
                </Link>
                <Link
                  href="/about"
                  className="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-navy transition-colors text-center"
                >
                  Learn About Us
                </Link>
              </div>
            </div>
            <div className="relative">
              <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8">
                <Image
                  src="/image.png"
                  alt="NHM Group Holdings Business"
                  width={600}
                  height={400}
                  className="rounded-lg shadow-2xl"
                  priority
                />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Mission Statement */}
      <section className="section-padding bg-light-gray">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-4xl font-bold font-serif text-navy mb-8">Our Mission</h2>
          <p className="text-xl text-gray-700 max-w-4xl mx-auto leading-relaxed">
            To create sustainable value through strategic investments, operational excellence, and innovative
            partnerships across diverse industries. We are committed to building a legacy of success that
            benefits our stakeholders, communities, and the global economy.
          </p>
        </div>
      </section>

      {/* Portfolio Overview */}
      <section className="section-padding">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold font-serif text-navy mb-4">Our Portfolio</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Discover our diverse range of companies spanning automotive, technology, real estate, and more.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow">
              <div className="h-48 bg-gradient-to-br from-navy to-blue-600"></div>
              <div className="p-6">
                <h3 className="text-xl font-bold text-navy mb-2">Automotive Division</h3>
                <p className="text-gray-600 mb-4">
                  Leading automotive dealerships and services, including our flagship ISUZU operations in Libya.
                </p>
                <Link href="/companies/automotive" className="text-gold hover:text-yellow-600 font-semibold">
                  Learn More →
                </Link>
              </div>
            </div>

            <div className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow">
              <div className="h-48 bg-gradient-to-br from-gold to-yellow-500"></div>
              <div className="p-6">
                <h3 className="text-xl font-bold text-navy mb-2">Technology Solutions</h3>
                <p className="text-gray-600 mb-4">
                  Innovative technology companies driving digital transformation across various sectors.
                </p>
                <Link href="/companies/technology" className="text-gold hover:text-yellow-600 font-semibold">
                  Learn More →
                </Link>
              </div>
            </div>

            <div className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow">
              <div className="h-48 bg-gradient-to-br from-gray-600 to-gray-800"></div>
              <div className="p-6">
                <h3 className="text-xl font-bold text-navy mb-2">Real Estate</h3>
                <p className="text-gray-600 mb-4">
                  Strategic real estate investments and development projects in key markets.
                </p>
                <Link href="/companies/real-estate" className="text-gold hover:text-yellow-600 font-semibold">
                  Learn More →
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="hero-gradient text-white section-padding">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-4xl font-bold font-serif mb-6">Ready to Partner With Us?</h2>
          <p className="text-xl mb-8 text-blue-100 max-w-3xl mx-auto">
            Join us in building the future. Whether you're an investor, partner, or client,
            we're committed to creating mutual success.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/contact"
              className="bg-gold text-navy px-8 py-3 rounded-lg font-semibold hover:bg-yellow-400 transition-colors"
            >
              Get In Touch
            </Link>
            <Link
              href="/companies"
              className="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-navy transition-colors"
            >
              View Our Companies
            </Link>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-navy text-white py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="col-span-1 md:col-span-2">
              <h3 className="text-2xl font-bold font-serif mb-4">NHM Group Holdings</h3>
              <p className="text-blue-200 mb-4">
                Building tomorrow's success through strategic investments and operational excellence.
              </p>
            </div>
            <div>
              <h4 className="text-lg font-semibold mb-4">Quick Links</h4>
              <ul className="space-y-2">
                <li><Link href="/about" className="text-blue-200 hover:text-gold transition-colors">About Us</Link></li>
                <li><Link href="/companies" className="text-blue-200 hover:text-gold transition-colors">Our Companies</Link></li>
                <li><Link href="/clients" className="text-blue-200 hover:text-gold transition-colors">Clients & Partners</Link></li>
                <li><Link href="/contact" className="text-blue-200 hover:text-gold transition-colors">Contact</Link></li>
              </ul>
            </div>
            <div>
              <h4 className="text-lg font-semibold mb-4">Contact Info</h4>
              <p className="text-blue-200 mb-2">Email: <EMAIL></p>
              <p className="text-blue-200 mb-2">Phone: +****************</p>
            </div>
          </div>
          <div className="border-t border-blue-800 mt-8 pt-8 text-center">
            <p className="text-blue-200">&copy; 2024 NHM Group Holdings. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}
