import Image from "next/image";
import Link from "next/link";

export default function Home() {
  return (
    <div className="min-h-screen bg-white">
      {/* Clean Professional Navigation */}
      <nav className="nav-clean fixed top-0 w-full z-50">
        <div className="max-w-7xl mx-auto px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-3">
              <Image
                src="/logo.png"
                alt="NHM Group Holdings Logo"
                width={50}
                height={50}
                className="object-contain"
                priority
              />
              <div className="text-xl font-bold">
                <span className="text-primary">NHM Group</span>
                <span className="text-gray-600 ml-2 font-normal">Holdings</span>
              </div>
            </div>
            <div className="hidden lg:block">
              <div className="flex items-center space-x-8">
                <Link href="/" className="text-primary hover:text-green-600 transition-colors text-sm font-medium">
                  Home
                </Link>
                <Link href="/about" className="text-gray-600 hover:text-primary transition-colors text-sm font-medium">
                  About Us
                </Link>
                <Link href="/companies" className="text-gray-600 hover:text-primary transition-colors text-sm font-medium">
                  Business Sectors
                </Link>
                <Link href="/clients" className="text-gray-600 hover:text-primary transition-colors text-sm font-medium">
                  Partners
                </Link>
                <Link href="/contact" className="btn-green px-6 py-2 rounded-lg text-sm font-medium">
                  Contact
                </Link>
              </div>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section - Al Sahl Inspired */}
      <section className="hero-gradient pt-20 pb-16">
        <div className="max-w-7xl mx-auto px-6 lg:px-8">
          <div className="text-center mb-16">
            <h1 className="text-4xl lg:text-6xl font-bold mb-6">
              <span className="text-gray-800">Investing in Libyan and Regional</span>
              <br />
              <span className="heading-luxury">Development Since 1959</span>
            </h1>
            <p className="text-xl lg:text-2xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
              NHM Group Holdings is a leading Libyan investment and development company active across
              a wide cross-section of industries vital to Libya and Regional development.
            </p>
            <div className="mt-8">
              <Link
                href="/about"
                className="btn-green px-8 py-3 rounded-lg font-semibold text-lg"
              >
                Learn More
              </Link>
            </div>
          </div>

          {/* Stats Section */}
          <div className="bg-white rounded-2xl shadow-lg p-8 lg:p-12">
            <h2 className="text-2xl lg:text-3xl font-bold text-center mb-2 text-gray-800">
              Group Accomplishments
            </h2>
            <p className="text-center text-gray-600 mb-12">In Libya</p>

            <div className="grid grid-cols-2 lg:grid-cols-5 gap-8">
              <div className="text-center">
                <div className="text-4xl lg:text-5xl font-bold text-green-gradient mb-2">12</div>
                <div className="text-sm text-gray-600 font-medium">Markets</div>
              </div>
              <div className="text-center">
                <div className="text-4xl lg:text-5xl font-bold text-green-gradient mb-2">150+</div>
                <div className="text-sm text-gray-600 font-medium">Partners</div>
              </div>
              <div className="text-center">
                <div className="text-4xl lg:text-5xl font-bold text-green-gradient mb-2">65</div>
                <div className="text-sm text-gray-600 font-medium">Years In Business</div>
              </div>
              <div className="text-center">
                <div className="text-4xl lg:text-5xl font-bold text-green-gradient mb-2">6</div>
                <div className="text-sm text-gray-600 font-medium">Business Sectors</div>
              </div>
              <div className="text-center">
                <div className="text-4xl lg:text-5xl font-bold text-green-gradient mb-2">46+</div>
                <div className="text-sm text-gray-600 font-medium">Brands</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Business Sectors Section */}
      <section className="section-light py-16">
        <div className="max-w-7xl mx-auto px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold mb-4 text-gray-800">
              Transforming Libya and Regional Economies via 6 Business Sectors
            </h2>
            <p className="text-lg text-gray-600 max-w-4xl mx-auto">
              Our main activities are investing in, producing, importing, marketing, and distributing
              products & services across six business sectors to our target business and retail consumers.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <Link href="/companies/industrial" className="business-card p-8 text-center group">
              <div className="w-16 h-16 mx-auto mb-6 bg-gradient-to-br from-green-600 to-green-700 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                <span className="text-2xl text-white">🏭</span>
              </div>
              <h3 className="text-xl font-bold text-gray-800 mb-3 group-hover:text-primary transition-colors">Industrial</h3>
              <p className="text-gray-600 leading-relaxed">
                Manufacturing and industrial solutions driving economic growth and development across the region.
              </p>
            </Link>

            <Link href="/companies/agribusiness" className="business-card p-8 text-center group">
              <div className="w-16 h-16 mx-auto mb-6 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                <span className="text-2xl text-white">🌾</span>
              </div>
              <h3 className="text-xl font-bold text-gray-800 mb-3 group-hover:text-primary transition-colors">Agribusiness</h3>
              <p className="text-gray-600 leading-relaxed">
                Agricultural innovation and food production supporting regional food security and sustainability.
              </p>
            </Link>

            <Link href="/companies/healthcare" className="business-card p-8 text-center group">
              <div className="w-16 h-16 mx-auto mb-6 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                <span className="text-2xl text-white">🏥</span>
              </div>
              <h3 className="text-xl font-bold text-gray-800 mb-3 group-hover:text-primary transition-colors">Healthcare</h3>
              <p className="text-gray-600 leading-relaxed">
                Medical services and healthcare solutions improving quality of life for communities.
              </p>
            </Link>

            <Link href="/companies/trade" className="business-card p-8 text-center group">
              <div className="w-16 h-16 mx-auto mb-6 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                <span className="text-2xl text-white">🚛</span>
              </div>
              <h3 className="text-xl font-bold text-gray-800 mb-3 group-hover:text-primary transition-colors">Trade & Distribution</h3>
              <p className="text-gray-600 leading-relaxed">
                Supply chain and distribution networks connecting markets and delivering value.
              </p>
            </Link>

            <Link href="/companies/real-estate" className="business-card p-8 text-center group">
              <div className="w-16 h-16 mx-auto mb-6 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                <span className="text-2xl text-white">🏢</span>
              </div>
              <h3 className="text-xl font-bold text-gray-800 mb-3 group-hover:text-primary transition-colors">Real Estate & Construction</h3>
              <p className="text-gray-600 leading-relaxed">
                Property development and construction projects shaping modern infrastructure.
              </p>
            </Link>

            <Link href="/companies/services" className="business-card p-8 text-center group">
              <div className="w-16 h-16 mx-auto mb-6 bg-gradient-to-br from-indigo-500 to-indigo-600 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                <span className="text-2xl text-white">⚙️</span>
              </div>
              <h3 className="text-xl font-bold text-gray-800 mb-3 group-hover:text-primary transition-colors">Services</h3>
              <p className="text-gray-600 leading-relaxed">
                Professional services and solutions supporting business growth and operational excellence.
              </p>
            </Link>
          </div>
        </div>
      </section>

      {/* Brands & Quality Section */}
      <section className="section-green py-16">
        <div className="max-w-7xl mx-auto px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold mb-4 text-gray-800">
              A World Class Portfolio of Over 46 Own and International Brands
            </h2>
            <p className="text-lg text-gray-600 max-w-4xl mx-auto">
              NHM Group Holdings is single-mindedly focused on improving the Quality of Life for Libyans
              and for consumers in the regional markets we serve. This commitment is fulfilled via our
              world class portfolio of own and international brands.
            </p>
          </div>

          <div className="bg-white rounded-2xl shadow-lg p-8 lg:p-12 mb-16">
            <div className="text-center mb-12">
              <h3 className="text-2xl lg:text-3xl font-bold mb-4 text-gray-800">
                We are focused on improving <span className="text-green-gradient">quality</span> of life
              </h3>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <div>
                <h4 className="text-xl font-bold mb-6 text-gray-800">
                  At NHM Group Holdings, Quality is a culture and one of our core values
                </h4>
                <p className="text-gray-600 leading-relaxed mb-6">
                  Quality and Safety have always been part of the foundation that has shaped the world-class
                  image and trust we have earned from our customers. Thanks to our quality-centric culture
                  driven by the seasoned professionals that comprise our staff, and thanks to our world-class
                  and globally dispersed production ecosystem.
                </p>
                <p className="text-gray-600 leading-relaxed">
                  NHM has been awarded the following Quality Assurance certifications by leading industry standards bodies.
                </p>
              </div>

              <div className="grid grid-cols-2 gap-8">
                <div className="stats-card p-6 rounded-xl text-center">
                  <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-green-600 to-green-700 rounded-full flex items-center justify-center">
                    <span className="text-white font-bold">ISO</span>
                  </div>
                  <h5 className="font-bold text-gray-800 mb-2">ISO 9001</h5>
                  <p className="text-sm text-gray-600">Quality Management</p>
                </div>

                <div className="stats-card p-6 rounded-xl text-center">
                  <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-blue-600 to-blue-700 rounded-full flex items-center justify-center">
                    <span className="text-white font-bold">TÜV</span>
                  </div>
                  <h5 className="font-bold text-gray-800 mb-2">TÜV SÜD</h5>
                  <p className="text-sm text-gray-600">Safety Standards</p>
                </div>
              </div>
            </div>
          </div>

          <div className="text-center">
            <Link href="/brands" className="btn-outline-green px-8 py-3 rounded-lg font-semibold">
              View Our Brands
            </Link>
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section className="section-white py-16">
        <div className="max-w-4xl mx-auto px-6 lg:px-8 text-center">
          <h2 className="text-3xl lg:text-4xl font-bold mb-6 text-gray-800">
            Ready to Partner With Us?
          </h2>
          <p className="text-lg text-gray-600 mb-8 max-w-2xl mx-auto">
            Join us in building the future. Whether you're an investor, partner, or client,
            we're committed to creating mutual success and sustainable growth.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/contact"
              className="btn-green px-8 py-3 rounded-lg font-semibold"
            >
              Get In Touch
            </Link>
            <Link
              href="/companies"
              className="btn-outline-green px-8 py-3 rounded-lg font-semibold"
            >
              View Our Companies
            </Link>
          </div>
        </div>
      </section>

      {/* Clean Footer */}
      <footer className="bg-gray-800 text-white">
        <div className="max-w-7xl mx-auto px-6 lg:px-8 py-12">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="col-span-1 md:col-span-2">
              <div className="flex items-center space-x-3 mb-4">
                <Image
                  src="/logo.png"
                  alt="NHM Group Holdings Logo"
                  width={40}
                  height={40}
                  className="object-contain"
                />
                <div className="text-lg font-bold">
                  <span className="text-white">NHM Group Holdings</span>
                </div>
              </div>
              <p className="text-gray-300 mb-6 max-w-md">
                NHM Group Holdings is active across a wide cross-section of industries
                vital to Libya and Regional development.
              </p>
              <div className="flex space-x-4">
                <div className="w-10 h-10 bg-green-600 rounded-lg flex items-center justify-center hover:bg-green-700 transition-colors cursor-pointer">
                  <span className="text-white font-bold">F</span>
                </div>
                <div className="w-10 h-10 bg-green-600 rounded-lg flex items-center justify-center hover:bg-green-700 transition-colors cursor-pointer">
                  <span className="text-white font-bold">L</span>
                </div>
              </div>
            </div>

            <div>
              <h4 className="text-lg font-semibold mb-4">Navigation</h4>
              <ul className="space-y-2">
                <li><Link href="/" className="text-gray-300 hover:text-white transition-colors">Home</Link></li>
                <li><Link href="/about" className="text-gray-300 hover:text-white transition-colors">About Us</Link></li>
                <li><Link href="/companies" className="text-gray-300 hover:text-white transition-colors">Business Sectors</Link></li>
                <li><Link href="/clients" className="text-gray-300 hover:text-white transition-colors">Partners</Link></li>
                <li><Link href="/contact" className="text-gray-300 hover:text-white transition-colors">Contact</Link></li>
              </ul>
            </div>

            <div>
              <h4 className="text-lg font-semibold mb-4">Contact</h4>
              <div className="space-y-2 text-gray-300">
                <p>Head Office: Al-Ghiran, Tripoli-Libya</p>
                <p>PO Box 89086</p>
                <p>+218 21 487 0833</p>
                <p>+218 21 487 0834</p>
                <p>+218 21 487 0835</p>
                <p><EMAIL></p>
              </div>
            </div>
          </div>

          <div className="border-t border-gray-700 mt-8 pt-8 text-center">
            <p className="text-gray-400">
              &copy; All Rights Reserved 2025 – NHM Group Holdings
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
}
