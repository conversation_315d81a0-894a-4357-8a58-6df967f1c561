import Image from "next/image";
import Link from "next/link";

export default function Home() {
  return (
    <div className="min-h-screen bg-white">
      {/* Clean Professional Navigation */}
      <nav className="nav-clean fixed top-0 w-full z-50">
        <div className="max-w-7xl mx-auto px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-3">
              <Image
                src="/logo.png"
                alt="NHM Group Holdings Logo"
                width={50}
                height={50}
                className="object-contain"
                priority
              />
              <div className="text-xl font-bold">
                <span className="text-primary">NHM Group</span>
                <span className="text-gray-600 ml-2 font-normal">Holdings</span>
              </div>
            </div>
            <div className="hidden lg:block">
              <div className="flex items-center space-x-8">
                <Link href="/" className="text-primary hover:text-green-600 transition-colors text-sm font-medium">
                  Home
                </Link>
                <Link href="/about" className="text-gray-600 hover:text-primary transition-colors text-sm font-medium">
                  About Us
                </Link>
                <Link href="/companies" className="text-gray-600 hover:text-primary transition-colors text-sm font-medium">
                  Business Sectors
                </Link>
                <Link href="/clients" className="text-gray-600 hover:text-primary transition-colors text-sm font-medium">
                  Partners
                </Link>
                <Link href="/contact" className="btn-green px-6 py-2 rounded-lg text-sm font-medium">
                  Contact
                </Link>
              </div>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section - Al Sahl Inspired */}
      <section className="hero-gradient pt-20 pb-16">
        <div className="max-w-7xl mx-auto px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center mb-16">
            <div>
              <h1 className="text-4xl lg:text-5xl font-bold mb-6">
                <span className="text-gray-800">Investing in Libyan and Regional</span>
                <br />
                <span className="heading-luxury">Development Since 1959</span>
              </h1>
              <p className="text-xl text-gray-600 leading-relaxed mb-8">
                NHM Group Holdings is a leading Libyan investment and development company active across
                a wide cross-section of industries vital to Libya and Regional development.
              </p>
              <div className="flex flex-col sm:flex-row gap-4">
                <Link
                  href="/about"
                  className="btn-green px-8 py-3 rounded-lg font-semibold text-lg"
                >
                  Learn More
                </Link>
                <Link
                  href="/companies"
                  className="btn-outline-green px-8 py-3 rounded-lg font-semibold text-lg"
                >
                  Our Sectors
                </Link>
              </div>
            </div>

            <div className="relative">
              <div className="business-card p-6 rounded-2xl">
                <Image
                  src="/image.png"
                  alt="NHM Group Holdings - Business Excellence"
                  width={600}
                  height={400}
                  className="w-full h-auto object-cover rounded-xl"
                  priority
                />
              </div>
            </div>
          </div>

          {/* Stats Section */}
          <div className="bg-white rounded-2xl shadow-lg overflow-hidden">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-0">
              <div className="lg:col-span-2 p-8 lg:p-12">
                <h2 className="text-2xl lg:text-3xl font-bold mb-2 text-gray-800">
                  Group Accomplishments
                </h2>
                <p className="text-gray-600 mb-8">In Libya</p>

                <div className="grid grid-cols-2 lg:grid-cols-3 gap-6">
                  <div className="text-center">
                    <div className="text-3xl lg:text-4xl font-bold text-green-gradient mb-2">12</div>
                    <div className="text-sm text-gray-600 font-medium">Markets</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl lg:text-4xl font-bold text-green-gradient mb-2">150+</div>
                    <div className="text-sm text-gray-600 font-medium">Partners</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl lg:text-4xl font-bold text-green-gradient mb-2">65</div>
                    <div className="text-sm text-gray-600 font-medium">Years In Business</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl lg:text-4xl font-bold text-green-gradient mb-2">6</div>
                    <div className="text-sm text-gray-600 font-medium">Business Sectors</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl lg:text-4xl font-bold text-green-gradient mb-2">46+</div>
                    <div className="text-sm text-gray-600 font-medium">Brands</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl lg:text-4xl font-bold text-green-gradient mb-2">1000+</div>
                    <div className="text-sm text-gray-600 font-medium">Employees</div>
                  </div>
                </div>
              </div>

              <div className="relative h-64 lg:h-auto">
                <Image
                  src="https://images.unsplash.com/photo-1454165804606-c3d57bc86b40?w=400&h=600&fit=crop&crop=center"
                  alt="Business Growth and Success"
                  width={400}
                  height={600}
                  className="w-full h-full object-cover"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-green-600/20 to-transparent"></div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Business Sectors Section */}
      <section className="section-light py-16">
        <div className="max-w-7xl mx-auto px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold mb-4 text-gray-800">
              Transforming Libya and Regional Economies via 6 Business Sectors
            </h2>
            <p className="text-lg text-gray-600 max-w-4xl mx-auto">
              Our main activities are investing in, producing, importing, marketing, and distributing
              products & services across six business sectors to our target business and retail consumers.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <Link href="/companies/industrial" className="business-card group overflow-hidden">
              <div className="h-48 bg-gradient-to-br from-gray-100 to-gray-200 relative overflow-hidden">
                <Image
                  src="https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?w=400&h=300&fit=crop&crop=center"
                  alt="Industrial Manufacturing"
                  width={400}
                  height={300}
                  className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                />
                <div className="absolute inset-0 bg-green-600/20 group-hover:bg-green-600/30 transition-colors"></div>
              </div>
              <div className="p-6">
                <h3 className="text-xl font-bold text-gray-800 mb-3 group-hover:text-primary transition-colors">Industrial</h3>
                <p className="text-gray-600 leading-relaxed">
                  Manufacturing and industrial solutions driving economic growth and development across the region.
                </p>
              </div>
            </Link>

            <Link href="/companies/agribusiness" className="business-card group overflow-hidden">
              <div className="h-48 bg-gradient-to-br from-gray-100 to-gray-200 relative overflow-hidden">
                <Image
                  src="https://images.unsplash.com/photo-1574323347407-f5e1ad6d020b?w=400&h=300&fit=crop&crop=center"
                  alt="Agriculture and Farming"
                  width={400}
                  height={300}
                  className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                />
                <div className="absolute inset-0 bg-green-600/20 group-hover:bg-green-600/30 transition-colors"></div>
              </div>
              <div className="p-6">
                <h3 className="text-xl font-bold text-gray-800 mb-3 group-hover:text-primary transition-colors">Agribusiness</h3>
                <p className="text-gray-600 leading-relaxed">
                  Agricultural innovation and food production supporting regional food security and sustainability.
                </p>
              </div>
            </Link>

            <Link href="/companies/healthcare" className="business-card group overflow-hidden">
              <div className="h-48 bg-gradient-to-br from-gray-100 to-gray-200 relative overflow-hidden">
                <Image
                  src="https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?w=400&h=300&fit=crop&crop=center"
                  alt="Healthcare and Medical Services"
                  width={400}
                  height={300}
                  className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                />
                <div className="absolute inset-0 bg-blue-600/20 group-hover:bg-blue-600/30 transition-colors"></div>
              </div>
              <div className="p-6">
                <h3 className="text-xl font-bold text-gray-800 mb-3 group-hover:text-primary transition-colors">Healthcare</h3>
                <p className="text-gray-600 leading-relaxed">
                  Medical services and healthcare solutions improving quality of life for communities.
                </p>
              </div>
            </Link>

            <Link href="/companies/trade" className="business-card group overflow-hidden">
              <div className="h-48 bg-gradient-to-br from-gray-100 to-gray-200 relative overflow-hidden">
                <Image
                  src="https://images.unsplash.com/photo-1586528116311-ad8dd3c8310d?w=400&h=300&fit=crop&crop=center"
                  alt="Trade and Distribution"
                  width={400}
                  height={300}
                  className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                />
                <div className="absolute inset-0 bg-orange-600/20 group-hover:bg-orange-600/30 transition-colors"></div>
              </div>
              <div className="p-6">
                <h3 className="text-xl font-bold text-gray-800 mb-3 group-hover:text-primary transition-colors">Trade & Distribution</h3>
                <p className="text-gray-600 leading-relaxed">
                  Supply chain and distribution networks connecting markets and delivering value.
                </p>
              </div>
            </Link>

            <Link href="/companies/real-estate" className="business-card group overflow-hidden">
              <div className="h-48 bg-gradient-to-br from-gray-100 to-gray-200 relative overflow-hidden">
                <Image
                  src="https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?w=400&h=300&fit=crop&crop=center"
                  alt="Real Estate and Construction"
                  width={400}
                  height={300}
                  className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                />
                <div className="absolute inset-0 bg-purple-600/20 group-hover:bg-purple-600/30 transition-colors"></div>
              </div>
              <div className="p-6">
                <h3 className="text-xl font-bold text-gray-800 mb-3 group-hover:text-primary transition-colors">Real Estate & Construction</h3>
                <p className="text-gray-600 leading-relaxed">
                  Property development and construction projects shaping modern infrastructure.
                </p>
              </div>
            </Link>

            <Link href="/companies/services" className="business-card group overflow-hidden">
              <div className="h-48 bg-gradient-to-br from-gray-100 to-gray-200 relative overflow-hidden">
                <Image
                  src="https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=400&h=300&fit=crop&crop=center"
                  alt="Professional Services"
                  width={400}
                  height={300}
                  className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                />
                <div className="absolute inset-0 bg-indigo-600/20 group-hover:bg-indigo-600/30 transition-colors"></div>
              </div>
              <div className="p-6">
                <h3 className="text-xl font-bold text-gray-800 mb-3 group-hover:text-primary transition-colors">Services</h3>
                <p className="text-gray-600 leading-relaxed">
                  Professional services and solutions supporting business growth and operational excellence.
                </p>
              </div>
            </Link>
          </div>
        </div>
      </section>

      {/* Brands & Quality Section */}
      <section className="section-green py-16">
        <div className="max-w-7xl mx-auto px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold mb-4 text-gray-800">
              A World Class Portfolio of Over 46 Own and International Brands
            </h2>
            <p className="text-lg text-gray-600 max-w-4xl mx-auto">
              NHM Group Holdings is single-mindedly focused on improving the Quality of Life for Libyans
              and for consumers in the regional markets we serve. This commitment is fulfilled via our
              world class portfolio of own and international brands.
            </p>
          </div>

          <div className="bg-white rounded-2xl shadow-lg p-8 lg:p-12 mb-16">
            <div className="text-center mb-12">
              <h3 className="text-2xl lg:text-3xl font-bold mb-4 text-gray-800">
                We are focused on improving <span className="text-green-gradient">quality</span> of life
              </h3>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-12 items-center">
              <div className="lg:col-span-2">
                <h4 className="text-xl font-bold mb-6 text-gray-800">
                  At NHM Group Holdings, Quality is a culture and one of our core values
                </h4>
                <p className="text-gray-600 leading-relaxed mb-6">
                  Quality and Safety have always been part of the foundation that has shaped the world-class
                  image and trust we have earned from our customers. Thanks to our quality-centric culture
                  driven by the seasoned professionals that comprise our staff, and thanks to our world-class
                  and globally dispersed production ecosystem.
                </p>
                <p className="text-gray-600 leading-relaxed mb-8">
                  NHM has been awarded the following Quality Assurance certifications by leading industry standards bodies.
                </p>

                <div className="grid grid-cols-2 gap-6">
                  <div className="stats-card p-6 rounded-xl text-center">
                    <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-green-600 to-green-700 rounded-full flex items-center justify-center">
                      <span className="text-white font-bold">ISO</span>
                    </div>
                    <h5 className="font-bold text-gray-800 mb-2">ISO 9001</h5>
                    <p className="text-sm text-gray-600">Quality Management</p>
                  </div>

                  <div className="stats-card p-6 rounded-xl text-center">
                    <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-blue-600 to-blue-700 rounded-full flex items-center justify-center">
                      <span className="text-white font-bold">TÜV</span>
                    </div>
                    <h5 className="font-bold text-gray-800 mb-2">TÜV SÜD</h5>
                    <p className="text-sm text-gray-600">Safety Standards</p>
                  </div>
                </div>
              </div>

              <div className="business-card p-6 rounded-xl">
                <Image
                  src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=500&fit=crop&crop=center"
                  alt="Quality Assurance and Professional Standards"
                  width={400}
                  height={500}
                  className="w-full h-auto object-cover rounded-lg"
                />
              </div>
            </div>
          </div>

          <div className="text-center">
            <Link href="/brands" className="btn-outline-green px-8 py-3 rounded-lg font-semibold">
              View Our Brands
            </Link>
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section className="relative py-16 overflow-hidden">
        <div className="absolute inset-0">
          <Image
            src="https://images.unsplash.com/photo-1497366216548-37526070297c?w=1200&h=600&fit=crop&crop=center"
            alt="Business Partnership and Collaboration"
            width={1200}
            height={600}
            className="w-full h-full object-cover"
          />
          <div className="absolute inset-0 bg-green-900/80"></div>
        </div>

        <div className="relative z-10 max-w-4xl mx-auto px-6 lg:px-8 text-center">
          <h2 className="text-3xl lg:text-4xl font-bold mb-6 text-white">
            Ready to Partner With Us?
          </h2>
          <p className="text-lg text-green-100 mb-8 max-w-2xl mx-auto">
            Join us in building the future. Whether you're an investor, partner, or client,
            we're committed to creating mutual success and sustainable growth.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/contact"
              className="bg-white text-green-800 hover:bg-green-50 px-8 py-3 rounded-lg font-semibold transition-colors"
            >
              Get In Touch
            </Link>
            <Link
              href="/companies"
              className="border-2 border-white text-white hover:bg-white hover:text-green-800 px-8 py-3 rounded-lg font-semibold transition-colors"
            >
              View Our Companies
            </Link>
          </div>
        </div>
      </section>

      {/* Clean Footer */}
      <footer className="bg-gray-800 text-white">
        <div className="max-w-7xl mx-auto px-6 lg:px-8 py-12">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="col-span-1 md:col-span-2">
              <div className="flex items-center space-x-3 mb-4">
                <Image
                  src="/logo.png"
                  alt="NHM Group Holdings Logo"
                  width={40}
                  height={40}
                  className="object-contain"
                />
                <div className="text-lg font-bold">
                  <span className="text-white">NHM Group Holdings</span>
                </div>
              </div>
              <p className="text-gray-300 mb-6 max-w-md">
                NHM Group Holdings is active across a wide cross-section of industries
                vital to Libya and Regional development.
              </p>
              <div className="flex space-x-4">
                <div className="w-10 h-10 bg-green-600 rounded-lg flex items-center justify-center hover:bg-green-700 transition-colors cursor-pointer">
                  <span className="text-white font-bold">F</span>
                </div>
                <div className="w-10 h-10 bg-green-600 rounded-lg flex items-center justify-center hover:bg-green-700 transition-colors cursor-pointer">
                  <span className="text-white font-bold">L</span>
                </div>
              </div>
            </div>

            <div>
              <h4 className="text-lg font-semibold mb-4">Navigation</h4>
              <ul className="space-y-2">
                <li><Link href="/" className="text-gray-300 hover:text-white transition-colors">Home</Link></li>
                <li><Link href="/about" className="text-gray-300 hover:text-white transition-colors">About Us</Link></li>
                <li><Link href="/companies" className="text-gray-300 hover:text-white transition-colors">Business Sectors</Link></li>
                <li><Link href="/clients" className="text-gray-300 hover:text-white transition-colors">Partners</Link></li>
                <li><Link href="/contact" className="text-gray-300 hover:text-white transition-colors">Contact</Link></li>
              </ul>
            </div>

            <div>
              <h4 className="text-lg font-semibold mb-4">Contact</h4>
              <div className="space-y-2 text-gray-300">
                <p>Head Office: Al-Ghiran, Tripoli-Libya</p>
                <p>PO Box 89086</p>
                <p>+218 21 487 0833</p>
                <p>+218 21 487 0834</p>
                <p>+218 21 487 0835</p>
                <p><EMAIL></p>
              </div>
            </div>
          </div>

          <div className="border-t border-gray-700 mt-8 pt-8 text-center">
            <p className="text-gray-400">
              &copy; All Rights Reserved 2025 – NHM Group Holdings
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
}
