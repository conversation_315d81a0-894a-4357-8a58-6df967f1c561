import Image from "next/image";
import Link from "next/link";

export default function Home() {
  return (
    <div className="min-h-screen bg-black">
      {/* Luxury Navigation */}
      <nav className="nav-luxury fixed top-0 w-full z-50">
        <div className="max-w-7xl mx-auto px-6 lg:px-8">
          <div className="flex justify-between items-center h-20">
            <div className="flex items-center">
              <div className="text-3xl font-bold font-serif">
                <span className="heading-luxury">NHM</span>
                <span className="text-white ml-2">GROUP</span>
                <div className="text-xs tracking-[0.3em] text-gold-gradient font-sans mt-1">
                  HOLDINGS
                </div>
              </div>
            </div>
            <div className="hidden lg:block">
              <div className="flex items-center space-x-12">
                <Link href="/" className="text-white hover:text-gold-gradient transition-all duration-300 text-sm font-medium tracking-wide relative group">
                  HOME
                  <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-gold to-gold-light transition-all duration-300 group-hover:w-full"></span>
                </Link>
                <Link href="/about" className="text-gray-300 hover:text-gold-gradient transition-all duration-300 text-sm font-medium tracking-wide relative group">
                  ABOUT
                  <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-gold to-gold-light transition-all duration-300 group-hover:w-full"></span>
                </Link>
                <Link href="/companies" className="text-gray-300 hover:text-gold-gradient transition-all duration-300 text-sm font-medium tracking-wide relative group">
                  PORTFOLIO
                  <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-gold to-gold-light transition-all duration-300 group-hover:w-full"></span>
                </Link>
                <Link href="/clients" className="text-gray-300 hover:text-gold-gradient transition-all duration-300 text-sm font-medium tracking-wide relative group">
                  PARTNERS
                  <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-gold to-gold-light transition-all duration-300 group-hover:w-full"></span>
                </Link>
                <Link href="/contact" className="btn-premium px-6 py-2 rounded-none text-sm font-medium tracking-wide text-black">
                  CONTACT
                </Link>
              </div>
            </div>
          </div>
        </div>
      </nav>

      {/* Premium Hero Section */}
      <section className="hero-gradient min-h-screen flex items-center relative overflow-hidden">
        {/* Floating Elements */}
        <div className="absolute top-20 right-20 w-32 h-32 bg-gradient-to-br from-gold/20 to-transparent rounded-full blur-xl animate-float"></div>
        <div className="absolute bottom-40 left-20 w-24 h-24 bg-gradient-to-br from-accent/20 to-transparent rounded-full blur-xl animate-float" style={{animationDelay: '1s'}}></div>

        <div className="max-w-7xl mx-auto px-6 lg:px-8 pt-20">
          <div className="grid grid-cols-1 lg:grid-cols-12 gap-16 items-center">
            <div className="lg:col-span-7 animate-fade-in-up">
              <div className="mb-6">
                <span className="text-gold text-sm font-medium tracking-[0.2em] uppercase">
                  Established Excellence
                </span>
              </div>

              <h1 className="text-6xl lg:text-7xl xl:text-8xl font-bold font-serif mb-8 leading-[0.9]">
                <span className="heading-luxury">Visionary</span>
                <br />
                <span className="text-white">Leadership</span>
                <br />
                <span className="text-gold-gradient">Exceptional</span>
                <br />
                <span className="text-white">Results</span>
              </h1>

              <div className="w-24 h-1 bg-gradient-to-r from-gold to-gold-light mb-8"></div>

              <p className="text-xl lg:text-2xl mb-12 text-gray-300 leading-relaxed max-w-2xl font-light">
                NHM Group Holdings stands as a beacon of strategic excellence, orchestrating a diverse portfolio
                of industry-leading enterprises across multiple sectors. We don't just invest—we transform.
              </p>

              <div className="flex flex-col sm:flex-row gap-6">
                <Link
                  href="/companies"
                  className="btn-premium px-10 py-4 text-black font-semibold tracking-wide text-center group"
                >
                  <span className="relative z-10">EXPLORE PORTFOLIO</span>
                </Link>
                <Link
                  href="/about"
                  className="btn-outline-premium px-10 py-4 font-semibold tracking-wide text-center relative z-10"
                >
                  DISCOVER OUR STORY
                </Link>
              </div>

              {/* Stats */}
              <div className="grid grid-cols-3 gap-8 mt-16 pt-16 border-t border-gray-700">
                <div className="text-center">
                  <div className="text-4xl font-bold text-gold-gradient mb-2">25+</div>
                  <div className="text-sm text-gray-400 tracking-wide">YEARS EXCELLENCE</div>
                </div>
                <div className="text-center">
                  <div className="text-4xl font-bold text-gold-gradient mb-2">15+</div>
                  <div className="text-sm text-gray-400 tracking-wide">PORTFOLIO COMPANIES</div>
                </div>
                <div className="text-center">
                  <div className="text-4xl font-bold text-gold-gradient mb-2">8</div>
                  <div className="text-sm text-gray-400 tracking-wide">INDUSTRY SECTORS</div>
                </div>
              </div>
            </div>

            <div className="lg:col-span-5 animate-slide-in-right">
              <div className="relative">
                {/* Premium Image Container */}
                <div className="premium-card p-8 rounded-3xl">
                  <div className="relative overflow-hidden rounded-2xl">
                    <Image
                      src="/image.png"
                      alt="NHM Group Holdings - Executive Excellence"
                      width={600}
                      height={700}
                      className="w-full h-auto object-cover"
                      priority
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-transparent"></div>
                  </div>
                </div>

                {/* Floating Achievement Badge */}
                <div className="absolute -bottom-6 -left-6 glass-effect p-6 rounded-2xl">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-gold-gradient mb-1">Industry</div>
                    <div className="text-sm text-gray-300">Leader</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Scroll Indicator */}
        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
          <div className="w-6 h-10 border-2 border-gold rounded-full flex justify-center">
            <div className="w-1 h-3 bg-gold rounded-full mt-2 animate-pulse"></div>
          </div>
        </div>
      </section>

      {/* Mission Statement */}
      <section className="section-dark section-padding">
        <div className="max-w-6xl mx-auto px-6 lg:px-8">
          <div className="text-center mb-20">
            <span className="text-gold text-sm font-medium tracking-[0.2em] uppercase mb-4 block">
              Our Purpose
            </span>
            <h2 className="text-5xl lg:text-6xl font-bold font-serif heading-luxury mb-8">
              Defining Excellence
            </h2>
            <div className="w-24 h-1 bg-gradient-to-r from-gold to-gold-light mx-auto mb-12"></div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
            <div className="premium-card p-8 rounded-2xl text-center group">
              <div className="w-16 h-16 mx-auto mb-6 bg-gradient-to-br from-gold to-gold-dark rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                <span className="text-2xl">🎯</span>
              </div>
              <h3 className="text-2xl font-bold text-white mb-4">Strategic Vision</h3>
              <p className="text-gray-300 leading-relaxed">
                We identify and capitalize on transformative opportunities across diverse industries,
                creating sustainable value through strategic foresight and operational excellence.
              </p>
            </div>

            <div className="premium-card p-8 rounded-2xl text-center group">
              <div className="w-16 h-16 mx-auto mb-6 bg-gradient-to-br from-accent to-navy-light rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                <span className="text-2xl">🤝</span>
              </div>
              <h3 className="text-2xl font-bold text-white mb-4">Partnership Excellence</h3>
              <p className="text-gray-300 leading-relaxed">
                Building lasting relationships with industry leaders, fostering innovation,
                and creating synergies that drive mutual success and sustainable growth.
              </p>
            </div>

            <div className="premium-card p-8 rounded-2xl text-center group">
              <div className="w-16 h-16 mx-auto mb-6 bg-gradient-to-br from-gold-light to-gold rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                <span className="text-2xl">🌟</span>
              </div>
              <h3 className="text-2xl font-bold text-white mb-4">Legacy Building</h3>
              <p className="text-gray-300 leading-relaxed">
                Committed to creating a lasting impact that benefits our stakeholders,
                communities, and the global economy for generations to come.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Premium Portfolio Section */}
      <section className="section-padding bg-gray-900 relative">
        <div className="max-w-7xl mx-auto px-6 lg:px-8">
          <div className="text-center mb-20">
            <span className="text-gold text-sm font-medium tracking-[0.2em] uppercase mb-4 block">
              Investment Portfolio
            </span>
            <h2 className="text-5xl lg:text-6xl font-bold font-serif heading-luxury mb-8">
              Diversified Excellence
            </h2>
            <div className="w-24 h-1 bg-gradient-to-r from-gold to-gold-light mx-auto mb-8"></div>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
              Our strategic investments span multiple industries, each carefully selected for their
              potential to deliver exceptional returns and sustainable growth.
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-16">
            {/* Featured Portfolio Item */}
            <Link href="/companies/automotive" className="group">
              <div className="premium-card p-8 rounded-3xl h-full relative overflow-hidden">
                <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-gold/20 to-transparent rounded-full blur-xl"></div>
                <div className="relative z-10">
                  <div className="flex items-center justify-between mb-6">
                    <div className="w-16 h-16 bg-gradient-to-br from-gold to-gold-dark rounded-2xl flex items-center justify-center">
                      <span className="text-2xl">🚛</span>
                    </div>
                    <span className="text-sm text-gold tracking-wide">FLAGSHIP</span>
                  </div>
                  <h3 className="text-3xl font-bold text-white mb-4 group-hover:text-gold-gradient transition-colors">
                    Automotive Division
                  </h3>
                  <p className="text-gray-300 mb-6 leading-relaxed">
                    Leading automotive dealerships and services across North Africa, featuring our
                    flagship ISUZU operations delivering excellence in commercial vehicle solutions.
                  </p>
                  <div className="flex items-center text-gold group-hover:text-gold-light transition-colors">
                    <span className="font-semibold tracking-wide">EXPLORE DIVISION</span>
                    <span className="ml-2 transform group-hover:translate-x-2 transition-transform">→</span>
                  </div>
                </div>
              </div>
            </Link>

            <div className="space-y-8">
              <Link href="/companies/technology" className="group block">
                <div className="premium-card p-6 rounded-2xl relative overflow-hidden">
                  <div className="flex items-center space-x-4">
                    <div className="w-12 h-12 bg-gradient-to-br from-accent to-navy-light rounded-xl flex items-center justify-center">
                      <span className="text-lg">💻</span>
                    </div>
                    <div className="flex-1">
                      <h4 className="text-xl font-bold text-white group-hover:text-gold-gradient transition-colors">
                        Technology Solutions
                      </h4>
                      <p className="text-gray-400 text-sm">Digital transformation & innovation</p>
                    </div>
                    <span className="text-gold group-hover:translate-x-2 transition-transform">→</span>
                  </div>
                </div>
              </Link>

              <Link href="/companies/real-estate" className="group block">
                <div className="premium-card p-6 rounded-2xl relative overflow-hidden">
                  <div className="flex items-center space-x-4">
                    <div className="w-12 h-12 bg-gradient-to-br from-gold-light to-gold rounded-xl flex items-center justify-center">
                      <span className="text-lg">🏢</span>
                    </div>
                    <div className="flex-1">
                      <h4 className="text-xl font-bold text-white group-hover:text-gold-gradient transition-colors">
                        Real Estate
                      </h4>
                      <p className="text-gray-400 text-sm">Strategic investments & development</p>
                    </div>
                    <span className="text-gold group-hover:translate-x-2 transition-transform">→</span>
                  </div>
                </div>
              </Link>

              <Link href="/companies/manufacturing" className="group block">
                <div className="premium-card p-6 rounded-2xl relative overflow-hidden">
                  <div className="flex items-center space-x-4">
                    <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-green-700 rounded-xl flex items-center justify-center">
                      <span className="text-lg">🏭</span>
                    </div>
                    <div className="flex-1">
                      <h4 className="text-xl font-bold text-white group-hover:text-gold-gradient transition-colors">
                        Manufacturing
                      </h4>
                      <p className="text-gray-400 text-sm">Industrial excellence & production</p>
                    </div>
                    <span className="text-gold group-hover:translate-x-2 transition-transform">→</span>
                  </div>
                </div>
              </Link>
            </div>
          </div>

          <div className="text-center">
            <Link href="/companies" className="btn-outline-premium px-12 py-4 font-semibold tracking-wide">
              VIEW COMPLETE PORTFOLIO
            </Link>
          </div>
        </div>
      </section>

      {/* Premium Call to Action */}
      <section className="section-dark section-padding relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-gold/5 via-transparent to-accent/5"></div>
        <div className="max-w-6xl mx-auto px-6 lg:px-8 text-center relative z-10">
          <div className="glass-effect p-16 rounded-3xl">
            <span className="text-gold text-sm font-medium tracking-[0.2em] uppercase mb-4 block">
              Partnership Opportunity
            </span>
            <h2 className="text-4xl lg:text-5xl font-bold font-serif heading-luxury mb-8">
              Shape the Future Together
            </h2>
            <div className="w-24 h-1 bg-gradient-to-r from-gold to-gold-light mx-auto mb-8"></div>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto mb-12 leading-relaxed">
              Join an exclusive network of visionary leaders, strategic investors, and industry pioneers.
              Together, we create opportunities that define tomorrow's success.
            </p>
            <div className="flex flex-col sm:flex-row gap-6 justify-center">
              <Link
                href="/contact"
                className="btn-premium px-12 py-4 text-black font-semibold tracking-wide"
              >
                INITIATE PARTNERSHIP
              </Link>
              <Link
                href="/companies"
                className="btn-outline-premium px-12 py-4 font-semibold tracking-wide"
              >
                EXPLORE OPPORTUNITIES
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Premium Footer */}
      <footer className="bg-black border-t border-gray-800">
        <div className="max-w-7xl mx-auto px-6 lg:px-8 py-16">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-12">
            <div className="lg:col-span-2">
              <div className="mb-8">
                <div className="text-3xl font-bold font-serif">
                  <span className="heading-luxury">NHM</span>
                  <span className="text-white ml-2">GROUP</span>
                  <div className="text-xs tracking-[0.3em] text-gold-gradient font-sans mt-1">
                    HOLDINGS
                  </div>
                </div>
              </div>
              <p className="text-gray-400 mb-8 leading-relaxed max-w-md">
                Defining excellence through strategic vision, operational mastery, and unwavering
                commitment to sustainable growth across diverse industries.
              </p>
              <div className="flex space-x-6">
                <div className="w-10 h-10 bg-gradient-to-br from-gold to-gold-dark rounded-lg flex items-center justify-center hover:scale-110 transition-transform cursor-pointer">
                  <span className="text-black font-bold">L</span>
                </div>
                <div className="w-10 h-10 bg-gradient-to-br from-accent to-navy-light rounded-lg flex items-center justify-center hover:scale-110 transition-transform cursor-pointer">
                  <span className="text-white font-bold">T</span>
                </div>
              </div>
            </div>

            <div>
              <h4 className="text-lg font-semibold text-white mb-6 tracking-wide">NAVIGATION</h4>
              <ul className="space-y-4">
                <li><Link href="/about" className="text-gray-400 hover:text-gold-gradient transition-colors">About Us</Link></li>
                <li><Link href="/companies" className="text-gray-400 hover:text-gold-gradient transition-colors">Portfolio</Link></li>
                <li><Link href="/clients" className="text-gray-400 hover:text-gold-gradient transition-colors">Partners</Link></li>
                <li><Link href="/contact" className="text-gray-400 hover:text-gold-gradient transition-colors">Contact</Link></li>
              </ul>
            </div>

            <div>
              <h4 className="text-lg font-semibold text-white mb-6 tracking-wide">CONNECT</h4>
              <div className="space-y-4">
                <div className="text-gray-400">
                  <span className="text-gold">Email:</span><br />
                  <EMAIL>
                </div>
                <div className="text-gray-400">
                  <span className="text-gold">Phone:</span><br />
                  +218 21 123 4567
                </div>
                <div className="text-gray-400">
                  <span className="text-gold">Location:</span><br />
                  Tripoli, Libya
                </div>
              </div>
            </div>
          </div>

          <div className="border-t border-gray-800 mt-16 pt-8">
            <div className="flex flex-col md:flex-row justify-between items-center">
              <p className="text-gray-500 text-sm">
                &copy; 2024 NHM Group Holdings. All rights reserved.
              </p>
              <div className="flex space-x-8 mt-4 md:mt-0">
                <Link href="#" className="text-gray-500 hover:text-gold-gradient text-sm transition-colors">
                  Privacy Policy
                </Link>
                <Link href="#" className="text-gray-500 hover:text-gold-gradient text-sm transition-colors">
                  Terms of Service
                </Link>
              </div>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
