"use client";

import Image from "next/image";
import Link from "next/link";
import { useState } from "react";

export default function Contact() {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    company: '',
    subject: '',
    message: ''
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Handle form submission here
    console.log('Form submitted:', formData);
    alert('Thank you for your message. We will get back to you soon!');
  };

  return (
    <div className="min-h-screen">
      {/* Navigation */}
      <nav className="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Link href="/">
                <h1 className="text-2xl font-bold text-blue-900 font-serif">NHM Group Holdings</h1>
              </Link>
            </div>
            <div className="hidden md:block">
              <div className="ml-10 flex items-baseline space-x-8">
                <Link href="/" className="text-gray-600 hover:text-yellow-600 transition-colors px-3 py-2 text-sm font-medium">
                  Home
                </Link>
                <Link href="/about" className="text-gray-600 hover:text-yellow-600 transition-colors px-3 py-2 text-sm font-medium">
                  About Us
                </Link>
                <Link href="/companies" className="text-gray-600 hover:text-yellow-600 transition-colors px-3 py-2 text-sm font-medium">
                  Our Companies
                </Link>
                <Link href="/clients" className="text-gray-600 hover:text-yellow-600 transition-colors px-3 py-2 text-sm font-medium">
                  Clients & Partners
                </Link>
                <Link href="/contact" className="text-blue-900 hover:text-yellow-600 transition-colors px-3 py-2 text-sm font-medium">
                  Contact
                </Link>
              </div>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="bg-gradient-to-r from-blue-900 to-blue-700 text-white py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-5xl font-bold font-serif mb-6">Contact Us</h1>
          <p className="text-xl text-blue-100 max-w-3xl mx-auto">
            Get in touch with our team to discuss partnership opportunities, investment inquiries, or learn more about our services
          </p>
        </div>
      </section>

      {/* Contact Form and Info */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {/* Contact Form */}
            <div>
              <h2 className="text-3xl font-bold font-serif text-blue-900 mb-8">Send Us a Message</h2>
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
                      Full Name *
                    </label>
                    <input
                      type="text"
                      id="name"
                      name="name"
                      required
                      value={formData.name}
                      onChange={handleChange}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="Your full name"
                    />
                  </div>
                  <div>
                    <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                      Email Address *
                    </label>
                    <input
                      type="email"
                      id="email"
                      name="email"
                      required
                      value={formData.email}
                      onChange={handleChange}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="<EMAIL>"
                    />
                  </div>
                </div>

                <div>
                  <label htmlFor="company" className="block text-sm font-medium text-gray-700 mb-2">
                    Company/Organization
                  </label>
                  <input
                    type="text"
                    id="company"
                    name="company"
                    value={formData.company}
                    onChange={handleChange}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Your company name"
                  />
                </div>

                <div>
                  <label htmlFor="subject" className="block text-sm font-medium text-gray-700 mb-2">
                    Subject *
                  </label>
                  <select
                    id="subject"
                    name="subject"
                    required
                    value={formData.subject}
                    onChange={handleChange}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="">Select a subject</option>
                    <option value="partnership">Partnership Inquiry</option>
                    <option value="investment">Investment Opportunity</option>
                    <option value="automotive">Automotive Services</option>
                    <option value="technology">Technology Solutions</option>
                    <option value="real-estate">Real Estate</option>
                    <option value="general">General Inquiry</option>
                  </select>
                </div>

                <div>
                  <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-2">
                    Message *
                  </label>
                  <textarea
                    id="message"
                    name="message"
                    required
                    rows={6}
                    value={formData.message}
                    onChange={handleChange}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Please provide details about your inquiry..."
                  ></textarea>
                </div>

                <button
                  type="submit"
                  className="w-full bg-blue-900 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-800 transition-colors"
                >
                  Send Message
                </button>
              </form>
            </div>

            {/* Contact Information */}
            <div>
              <h2 className="text-3xl font-bold font-serif text-blue-900 mb-8">Get In Touch</h2>
              
              <div className="space-y-8">
                <div className="flex items-start">
                  <div className="w-12 h-12 bg-blue-900 rounded-lg flex items-center justify-center mr-4 flex-shrink-0">
                    <span className="text-white text-xl">📍</span>
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-blue-900 mb-2">Headquarters</h3>
                    <p className="text-gray-600">
                      NHM Group Holdings<br />
                      Business District, Tower 1<br />
                      Tripoli, Libya<br />
                      P.O. Box 12345
                    </p>
                  </div>
                </div>

                <div className="flex items-start">
                  <div className="w-12 h-12 bg-yellow-400 rounded-lg flex items-center justify-center mr-4 flex-shrink-0">
                    <span className="text-blue-900 text-xl">📞</span>
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-blue-900 mb-2">Phone</h3>
                    <p className="text-gray-600">
                      Main: +218 21 123 4567<br />
                      Automotive: +218 21 123 4568<br />
                      Technology: +218 21 123 4569
                    </p>
                  </div>
                </div>

                <div className="flex items-start">
                  <div className="w-12 h-12 bg-green-600 rounded-lg flex items-center justify-center mr-4 flex-shrink-0">
                    <span className="text-white text-xl">✉️</span>
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-blue-900 mb-2">Email</h3>
                    <p className="text-gray-600">
                      General: <EMAIL><br />
                      Partnerships: <EMAIL><br />
                      Investments: <EMAIL>
                    </p>
                  </div>
                </div>

                <div className="flex items-start">
                  <div className="w-12 h-12 bg-purple-600 rounded-lg flex items-center justify-center mr-4 flex-shrink-0">
                    <span className="text-white text-xl">🕒</span>
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-blue-900 mb-2">Business Hours</h3>
                    <p className="text-gray-600">
                      Sunday - Thursday: 8:00 AM - 6:00 PM<br />
                      Friday: 8:00 AM - 12:00 PM<br />
                      Saturday: Closed
                    </p>
                  </div>
                </div>
              </div>

              {/* Office Image */}
              <div className="mt-8">
                <div className="bg-gradient-to-br from-blue-100 to-blue-50 rounded-2xl p-6">
                  <Image
                    src="/image copy 10.png"
                    alt="NHM Group Holdings Office"
                    width={500}
                    height={300}
                    className="rounded-lg shadow-lg w-full"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Office Locations */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold font-serif text-blue-900 mb-4">Our Locations</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Find us across multiple locations to serve you better
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="bg-white rounded-xl p-8 shadow-lg text-center">
              <div className="w-16 h-16 bg-blue-900 rounded-full flex items-center justify-center mx-auto mb-6">
                <span className="text-white text-2xl">🏢</span>
              </div>
              <h3 className="text-xl font-bold text-blue-900 mb-4">Tripoli Headquarters</h3>
              <p className="text-gray-600 mb-4">
                Main office and executive headquarters
              </p>
              <p className="text-sm text-gray-500">
                Business District, Tower 1<br />
                Tripoli, Libya
              </p>
            </div>

            <div className="bg-white rounded-xl p-8 shadow-lg text-center">
              <div className="w-16 h-16 bg-yellow-400 rounded-full flex items-center justify-center mx-auto mb-6">
                <span className="text-blue-900 text-2xl">🚛</span>
              </div>
              <h3 className="text-xl font-bold text-blue-900 mb-4">Automotive Center</h3>
              <p className="text-gray-600 mb-4">
                ISUZU dealership and service center
              </p>
              <p className="text-sm text-gray-500">
                Industrial Zone<br />
                Tripoli, Libya
              </p>
            </div>

            <div className="bg-white rounded-xl p-8 shadow-lg text-center">
              <div className="w-16 h-16 bg-green-600 rounded-full flex items-center justify-center mx-auto mb-6">
                <span className="text-white text-2xl">💻</span>
              </div>
              <h3 className="text-xl font-bold text-blue-900 mb-4">Technology Hub</h3>
              <p className="text-gray-600 mb-4">
                Innovation and technology center
              </p>
              <p className="text-sm text-gray-500">
                Tech Park<br />
                Tripoli, Libya
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="bg-gradient-to-r from-blue-900 to-blue-700 text-white py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-4xl font-bold font-serif mb-6">Ready to Start a Conversation?</h2>
          <p className="text-xl mb-8 text-blue-100 max-w-3xl mx-auto">
            Whether you're looking for partnership opportunities, investment options, or our services, 
            we're here to help you succeed.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link 
              href="/companies" 
              className="bg-yellow-400 text-blue-900 px-8 py-3 rounded-lg font-semibold hover:bg-yellow-300 transition-colors"
            >
              Explore Our Services
            </Link>
            <Link 
              href="/about" 
              className="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-blue-900 transition-colors"
            >
              Learn About Us
            </Link>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-blue-900 text-white py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="col-span-1 md:col-span-2">
              <h3 className="text-2xl font-bold font-serif mb-4">NHM Group Holdings</h3>
              <p className="text-blue-200 mb-4">
                Building tomorrow's success through strategic investments and operational excellence.
              </p>
            </div>
            <div>
              <h4 className="text-lg font-semibold mb-4">Quick Links</h4>
              <ul className="space-y-2">
                <li><Link href="/about" className="text-blue-200 hover:text-yellow-400 transition-colors">About Us</Link></li>
                <li><Link href="/companies" className="text-blue-200 hover:text-yellow-400 transition-colors">Our Companies</Link></li>
                <li><Link href="/clients" className="text-blue-200 hover:text-yellow-400 transition-colors">Clients & Partners</Link></li>
                <li><Link href="/contact" className="text-blue-200 hover:text-yellow-400 transition-colors">Contact</Link></li>
              </ul>
            </div>
            <div>
              <h4 className="text-lg font-semibold mb-4">Contact Info</h4>
              <p className="text-blue-200 mb-2">Email: <EMAIL></p>
              <p className="text-blue-200 mb-2">Phone: +218 21 123 4567</p>
            </div>
          </div>
          <div className="border-t border-blue-800 mt-8 pt-8 text-center">
            <p className="text-blue-200">&copy; 2024 NHM Group Holdings. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}
