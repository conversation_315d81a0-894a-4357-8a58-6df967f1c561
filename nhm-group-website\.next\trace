[{"name": "hot-reloader", "duration": 78, "timestamp": 418917811832, "id": 3, "tags": {"version": "15.3.4"}, "startTime": 1751043410455, "traceId": "49dec80f5bf38fa4"}, {"name": "setup-dev-bundler", "duration": 554305, "timestamp": 418917627717, "id": 2, "parentId": 1, "tags": {}, "startTime": 1751043410270, "traceId": "49dec80f5bf38fa4"}, {"name": "run-instrumentation-hook", "duration": 15, "timestamp": 418918228331, "id": 4, "parentId": 1, "tags": {}, "startTime": 1751043410871, "traceId": "49dec80f5bf38fa4"}, {"name": "start-dev-server", "duration": 1114374, "timestamp": 418917125901, "id": 1, "tags": {"cpus": "12", "platform": "win32", "memory.freeMem": "14676013056", "memory.totalMem": "33241804800", "memory.heapSizeLimit": "16670261248", "memory.rss": "177033216", "memory.heapTotal": "96112640", "memory.heapUsed": "69574200"}, "startTime": 1751043409769, "traceId": "49dec80f5bf38fa4"}, {"name": "compile-path", "duration": 1532286, "timestamp": 418940416695, "id": 7, "tags": {"trigger": "/"}, "startTime": 1751043433059, "traceId": "49dec80f5bf38fa4"}, {"name": "ensure-page", "duration": 1533349, "timestamp": 418940416169, "id": 6, "parentId": 3, "tags": {"inputPage": "/page"}, "startTime": 1751043433059, "traceId": "49dec80f5bf38fa4"}]